{"name": "jn-prompt-sheet", "group": "trs-jn", "version": "1.0.106", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "build-only": "vite build", "push": "node push.cjs", "push403": "npm run push 403", "type-check": "vue-tsc --build --force"}, "dependencies": {"axios": "^1.7.5", "echarts": "^5.5.1", "flatted": "^3.3.1", "less": "^4.2.0", "moment": "^2.30.1", "pinia": "^2.1.7", "vant": "^4.9.4", "vite-plugin-svg-icons": "^2.0.1", "vue": "^3.4.29", "vue-router": "^4.3.3"}, "devDependencies": {"@tsconfig/node20": "^20.1.4", "@types/echarts": "^4.9.22", "@types/node": "^20.14.5", "@vant/auto-import-resolver": "^1.2.1", "@vitejs/plugin-vue": "^5.0.5", "@vitejs/plugin-vue-jsx": "^4.0.0", "@vue/tsconfig": "^0.5.1", "npm-run-all2": "^6.2.0", "ora": "^1.2.0", "postcss-px-to-viewport": "^1.1.1", "scp2": "^0.5.0", "ssh2": "^1.15.0", "typescript": "~5.4.0", "unplugin-auto-import": "^0.18.2", "unplugin-vue-components": "^0.27.4", "vite": "^5.3.1", "vue-tsc": "^2.0.21"}}