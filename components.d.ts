/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    HeaderSearch: typeof import('./src/components/headerSearch/index.vue')['default']
    InfoContent: typeof import('./src/components/infoContent/index.vue')['default']
    Remark: typeof import('./src/components/remark/index.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    SvgIcon: typeof import('./src/components/SvgIcon/index.vue')['default']
    TipPopup: typeof import('./src/components/TipPopup/index.vue')['default']
    UrlIfram: typeof import('./src/components/urlIfram/index.vue')['default']
    VanBackTop: typeof import('vant/es')['BackTop']
    VanButton: typeof import('vant/es')['Button']
    VanEmpty: typeof import('vant/es')['Empty']
    VanField: typeof import('vant/es')['Field']
    VanHighlight: typeof import('vant/es')['Highlight']
    VanIcon: typeof import('vant/es')['Icon']
    VanList: typeof import('vant/es')['List']
    VanLoading: typeof import('vant/es')['Loading']
    VanNavBar: typeof import('vant/es')['NavBar']
    VanPullRefresh: typeof import('vant/es')['PullRefresh']
    VanSearch: typeof import('vant/es')['Search']
    VanSticky: typeof import('vant/es')['Sticky']
    VanTab: typeof import('vant/es')['Tab']
    VanTabbar: typeof import('vant/es')['Tabbar']
    VanTabbarItem: typeof import('vant/es')['TabbarItem']
    VanTabs: typeof import('vant/es')['Tabs']
    VanUploader: typeof import('vant/es')['Uploader']
  }
}
