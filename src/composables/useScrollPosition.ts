/**
 * 滚动位置保持组合式函数
 * 适用于新窗口打开详情页的场景，支持tab切换
 */
import { ref, nextTick } from 'vue'

interface ScrollPositionOptions {
  /** 页面唯一标识，用于区分不同页面的存储 */
  pageKey: string
  /** 滚动容器选择器，默认为页面根容器 */
  containerSelector?: string
  /** 是否支持tab切换，默认false */
  supportTabs?: boolean
  /** 当前激活的tab名称（支持tab时必传） */
  activeTab?: string
  /** 跳转模式：'newWindow' 新窗口打开 | 'currentPage' 当前页跳转 | 'externalLink' 外链跳转，默认'newWindow' */
  navigationMode?: 'newWindow' | 'currentPage' | 'externalLink'
  /** 保存滚动位置的延迟时间（毫秒），默认1000 */
  saveDelay?: number
  /** 恢复滚动位置的延迟时间（毫秒），默认100 */
  restoreDelay?: number
  /** 是否启用调试日志，默认false */
  debug?: boolean
}

interface PageData {
  /** 列表数据 */
  dataList: any[]
  /** 当前页码 */
  pageNo: number
  /** 是否已加载完成 */
  finished: boolean
  /** 数据总数 */
  count: number
  /** 搜索关键词（如果有） */
  keyword?: string
  /** 当前激活的tab */
  activeTab?: string
  /** 其他自定义数据 */
  [key: string]: any
}

export function useScrollPosition(options: ScrollPositionOptions) {
  const {
    pageKey,
    containerSelector,
    supportTabs = false,
    navigationMode = 'newWindow',
    saveDelay = 1000,
    restoreDelay = 100,
    debug = false
  } = options

  // 使用ref来跟踪当前的activeTab
  const currentActiveTab = ref(options.activeTab || '')

  // 生成存储key
  const getStorageKey = (suffix: string) => {
    const tabSuffix = supportTabs && currentActiveTab.value ? `_${currentActiveTab.value}` : ''
    return `${pageKey}${tabSuffix}_${suffix}`
  }

  const SCROLL_KEY = getStorageKey('scroll')
  const DATA_KEY = getStorageKey('data')

  let scrollTimer: number | null = null
  let isRestoring = ref(false)

  const log = (...args: any[]) => {
    if (debug) {
      console.log(`[useScrollPosition-${pageKey}]`, ...args)
    }
  }

  /**
   * 获取滚动容器
   */
  const getContainer = (): HTMLElement | null => {
    if (containerSelector) {
      const container = document.querySelector(containerSelector)
      if (container) return container as HTMLElement
    }

    // 默认查找常见的页面容器
    const selectors = [
      `.${pageKey}`,
      `[class*="${pageKey}"]`,
      '.van-tabs__content',
      '.van-tab__panel',
      'body'
    ]

    for (const selector of selectors) {
      const element = document.querySelector(selector)
      if (element && element.scrollHeight > element.clientHeight) {
        return element as HTMLElement
      }
    }

    return document.body
  }

  /**
   * 保存滚动位置和数据
   */
  const saveScrollAndData = (data?: Partial<PageData>) => {
    if (isRestoring.value) {
      log('正在恢复中，跳过保存')
      return
    }

    const container = getContainer()
    const scrollTop = container ? container.scrollTop : 0

    // 根据导航模式选择存储方式
    const storage = navigationMode === 'externalLink' ? localStorage : sessionStorage

    log('保存滚动位置:', scrollTop, '容器:', container?.className || container?.tagName, '存储方式:', navigationMode === 'externalLink' ? 'localStorage' : 'sessionStorage')

    // 保存滚动位置
    storage.setItem(SCROLL_KEY, scrollTop.toString())

    // 保存页面数据
    if (data) {
      const pageData = {
        ...data,
        activeTab: supportTabs ? currentActiveTab.value : undefined,
        timestamp: Date.now()
      }
      storage.setItem(DATA_KEY, JSON.stringify(pageData))
      log('保存页面数据:', pageData)
    }
  }

  /**
   * 恢复滚动位置
   */
  const restoreScrollPosition = () => {
    const storage = navigationMode === 'externalLink' ? localStorage : sessionStorage
    const savedScrollTop = storage.getItem(SCROLL_KEY)
    if (!savedScrollTop) {
      log('没有找到保存的滚动位置')
      return
    }

    const scrollTop = parseInt(savedScrollTop, 10)
    log('准备恢复滚动位置:', scrollTop, '存储方式:', navigationMode === 'externalLink' ? 'localStorage' : 'sessionStorage')

    isRestoring.value = true

    setTimeout(() => {
      const container = getContainer()
      if (container) {
        log('找到滚动容器:', container.className || container.tagName, '当前scrollTop:', container.scrollTop)
        container.scrollTop = scrollTop
        log('已设置滚动位置为:', scrollTop, '实际scrollTop:', container.scrollTop)
      } else {
        log('未找到滚动容器')
      }

      // 恢复完成后，延迟一段时间再允许保存
      setTimeout(() => {
        isRestoring.value = false
      }, 500)
    }, restoreDelay)
  }

  /**
   * 恢复页面数据
   */
  const restorePageData = (): PageData | null => {
    const storage = navigationMode === 'externalLink' ? localStorage : sessionStorage
    const savedData = storage.getItem(DATA_KEY)
    if (!savedData) return null

    try {
      const pageData = JSON.parse(savedData) as PageData

      // 外链模式检查时间更长（24小时），其他模式1小时
      const maxAge = navigationMode === 'externalLink' ? 24 * 60 * 60 * 1000 : 60 * 60 * 1000
      const now = Date.now()
      const dataTime = pageData.timestamp || 0
      if (now - dataTime > maxAge) {
        log('数据已过期，清除缓存')
        clearStoredData()
        return null
      }

      // 如果支持tab，检查tab是否匹配
      if (supportTabs && pageData.activeTab !== currentActiveTab.value) {
        log('tab不匹配，不恢复数据:', pageData.activeTab, '!=', currentActiveTab.value)
        return null
      }

      if (pageData.dataList && pageData.dataList.length > 0) {
        log('恢复页面数据:', pageData)
        return pageData
      }
    } catch (error) {
      log('恢复页面数据失败:', error)
    }

    return null
  }

  /**
   * 清除存储的数据
   */
  const clearStoredData = () => {
    const storage = navigationMode === 'externalLink' ? localStorage : sessionStorage
    storage.removeItem(SCROLL_KEY)
    storage.removeItem(DATA_KEY)
    log('已清除存储数据')
  }

  /**
   * 处理滚动事件
   */
  const handleScroll = () => {
    if (isRestoring.value) return
    
    if (scrollTimer) clearTimeout(scrollTimer)
    scrollTimer = setTimeout(() => {
      saveScrollAndData()
    }, saveDelay)
  }

  /**
   * 处理页面可见性变化（主要用于新窗口模式）
   */
  const handleVisibilityChange = () => {
    if (navigationMode === 'newWindow') {
      if (document.hidden) {
        // 页面失去焦点时保存数据
        log('页面失去焦点，保存数据')
        if (scrollTimer) {
          clearTimeout(scrollTimer)
          saveScrollAndData()
        }
      } else {
        // 页面重新获得焦点时恢复滚动位置
        log('页面重新获得焦点')
        setTimeout(() => {
          restoreScrollPosition()
        }, 200)
      }
    }
  }

  /**
   * 处理路由变化（用于当前页跳转模式）
   */
  const handleRouteChange = () => {
    if (navigationMode === 'currentPage') {
      log('检测到路由变化，准备恢复滚动位置')
      // 检查是否是从详情页返回
      const fromDetail = sessionStorage.getItem(`${pageKey}_fromDetail`)
      if (fromDetail === 'true') {
        log('从详情页返回，恢复滚动位置')
        setTimeout(() => {
          restoreScrollPosition()
        }, 200)
        sessionStorage.removeItem(`${pageKey}_fromDetail`)
      }
    }
  }

  /**
   * 添加滚动监听
   */
  const setupScrollListener = () => {
    nextTick(() => {
      const container = getContainer()
      if (container) {
        container.addEventListener('scroll', handleScroll, { passive: true })
        log('已添加滚动监听')
      }
    })
  }

  /**
   * 移除滚动监听
   */
  const removeScrollListener = () => {
    const container = getContainer()
    if (container) {
      container.removeEventListener('scroll', handleScroll)
      log('已移除滚动监听')
    }
    if (scrollTimer) {
      clearTimeout(scrollTimer)
      scrollTimer = null
    }
  }

  /**
   * 初始化
   */
  const init = () => {
    // 设置滚动监听
    setupScrollListener()

    if (navigationMode === 'newWindow') {
      // 新窗口模式：添加页面可见性监听
      document.addEventListener('visibilitychange', handleVisibilityChange)
    } else if (navigationMode === 'currentPage') {
      // 当前页模式：添加路由变化监听
      window.addEventListener('popstate', handleRouteChange)
      // 页面加载时也检查一次
      handleRouteChange()
    } else if (navigationMode === 'externalLink') {
      // 外链模式：页面加载时检查是否需要恢复
      // 外链跳转后返回时，页面会重新加载，所以在页面加载时恢复即可
      log('外链模式：页面加载时检查恢复状态')
    }

    log(`滚动位置管理已初始化 (${navigationMode} 模式)`)
  }

  /**
   * 销毁
   */
  const destroy = () => {
    removeScrollListener()

    if (navigationMode === 'newWindow') {
      document.removeEventListener('visibilitychange', handleVisibilityChange)
    } else if (navigationMode === 'currentPage') {
      window.removeEventListener('popstate', handleRouteChange)
    }
    // 外链模式不需要移除监听器

    log('滚动位置管理已销毁')
  }

  /**
   * 在点击详情链接前调用，保存当前状态
   */
  const beforeOpenDetail = (data?: Partial<PageData>) => {
    log('点击详情前保存状态')
    saveScrollAndData(data)

    // 如果是当前页跳转模式，设置返回标记
    if (navigationMode === 'currentPage') {
      sessionStorage.setItem(`${pageKey}_fromDetail`, 'true')
    }
  }

  /**
   * tab切换时调用，更新activeTab并清除当前tab的缓存
   */
  const onTabChange = (newTab: string) => {
    if (!supportTabs) return

    log('tab切换:', currentActiveTab.value, '->', newTab)

    // 更新当前activeTab
    currentActiveTab.value = newTab

    // 根据导航模式选择存储方式
    const storage = navigationMode === 'externalLink' ? localStorage : sessionStorage

    // 清除新tab的缓存，确保重新加载数据
    const newScrollKey = `${pageKey}_${newTab}_scroll`
    const newDataKey = `${pageKey}_${newTab}_data`
    storage.removeItem(newScrollKey)
    storage.removeItem(newDataKey)
  }

  /**
   * 更新当前activeTab
   */
  const updateActiveTab = (newTab: string) => {
    currentActiveTab.value = newTab
  }

  return {
    // 状态
    isRestoring,
    currentActiveTab,

    // 方法
    init,
    destroy,
    saveScrollAndData,
    restoreScrollPosition,
    restorePageData,
    clearStoredData,
    beforeOpenDetail,
    onTabChange,
    updateActiveTab,

    // 生命周期辅助
    onMounted: init,
    onBeforeUnmount: destroy
  }
}
