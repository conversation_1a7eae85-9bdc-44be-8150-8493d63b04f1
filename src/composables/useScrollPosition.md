# useScrollPosition 滚动位置保持组合式函数

## 功能说明

这个组合式函数用于保持列表页面的滚动位置和数据状态，支持两种跳转模式：新窗口打开详情页和当前页跳转到详情页。支持普通页面和带tab切换的页面。

## 适用场景

- **新窗口模式**：列表页面通过 `window.open()` 打开详情页
- **当前页模式**：列表页面通过路由跳转到详情页
- **外链模式**：列表页面通过 `window.location.href` 跳转到外部链接
- 用户返回列表页时，需要保持之前的滚动位置
- 支持tab切换的页面，每个tab独立保存状态
- 支持搜索功能的页面，保存搜索关键词

## 基本用法

### 1. 普通列表页面 - 新窗口模式

```typescript
import { useScrollPosition } from '@/composables/useScrollPosition'

// 在组件中使用（新窗口模式）
const scrollPosition = useScrollPosition({
  pageKey: 'publicSentiment',           // 页面唯一标识
  containerSelector: '.PublicSentiment', // 滚动容器选择器
  navigationMode: 'newWindow',          // 新窗口模式（默认）
  debug: true                           // 开启调试日志
})
```

### 2. 普通列表页面 - 当前页模式

```typescript
import { useScrollPosition } from '@/composables/useScrollPosition'

// 在组件中使用（当前页模式）
const scrollPosition = useScrollPosition({
  pageKey: 'publicSentiment',           // 页面唯一标识
  containerSelector: '.PublicSentiment', // 滚动容器选择器
  navigationMode: 'currentPage',        // 当前页跳转模式
  debug: true                           // 开启调试日志
})
```

### 3. 普通列表页面 - 外链模式（如涉济舆情）

```typescript
import { useScrollPosition } from '@/composables/useScrollPosition'

// 在组件中使用（外链模式）
const scrollPosition = useScrollPosition({
  pageKey: 'publicSentiment',           // 页面唯一标识
  containerSelector: '.PublicSentiment', // 滚动容器选择器
  navigationMode: 'externalLink',       // 外链跳转模式
  debug: true                           // 开启调试日志
})

// 在 onMounted 中初始化和恢复数据
onMounted(() => {
  const savedData = scrollPosition.restorePageData()
  if (savedData) {
    // 恢复数据
    dataList.value = savedData.dataList || []
    pageNo.value = savedData.pageNo || 0
    finished.value = savedData.finished || false
    count.value = savedData.count || 0
    keyword.value = savedData.keyword || ''
    
    // 恢复滚动位置
    scrollPosition.restoreScrollPosition()
  } else {
    // 正常加载数据
    loadData()
  }
  
  // 初始化滚动监听
  scrollPosition.onMounted()
})

// 在数据加载完成后保存状态
const loadData = () => {
  // ... 数据加载逻辑
  
  // 保存数据状态
  scrollPosition.saveScrollAndData({
    dataList: dataList.value,
    pageNo: pageNo.value,
    finished: finished.value,
    count: count.value,
    keyword: keyword.value
  })
}

// 页面卸载前保存数据
onBeforeUnmount(() => {
  scrollPosition.saveScrollAndData({
    dataList: dataList.value,
    pageNo: pageNo.value,
    finished: finished.value,
    count: count.value,
    keyword: keyword.value
  })
  scrollPosition.onBeforeUnmount()
})
```

### 4. 带tab切换的页面（如热搜榜单）

```typescript
import { useScrollPosition } from '@/composables/useScrollPosition'

const activeName = ref('头条热榜')

// 在组件中使用，支持tab切换
const scrollPosition = useScrollPosition({
  pageKey: 'hotList',
  containerSelector: '.hotList',
  supportTabs: true,                    // 启用tab支持
  activeTab: activeName.value,          // 当前激活的tab
  navigationMode: 'currentPage',        // 当前页跳转模式
  debug: true
})

// tab切换处理
const onClickTab = ({ title }) => {
  // 更新activeTab并通知滚动位置管理器
  activeName.value = title
  scrollPosition.updateActiveTab(title)
  scrollPosition.onTabChange(title)
  
  // 重置数据并加载新tab的数据
  pageNo.value = 0
  dataList.length = 0
  finished.value = false
  loadData()
}
```

### 5. 列表项组件中的使用

#### 新窗口模式

```typescript
const openURL = () => {
  // 在打开新窗口前保存当前滚动位置
  const container = document.querySelector('.PublicSentiment') // 或对应的容器选择器
  if (container) {
    const scrollTop = container.scrollTop
    sessionStorage.setItem('publicSentiment_scroll', scrollTop.toString())
    console.log('点击详情前保存滚动位置:', scrollTop)
  }

  window.open(itemInfo.url, '_blank')
}
```

#### 当前页模式

```typescript
import { useRouter } from 'vue-router'

const router = useRouter()

const openURL = () => {
  // 保存当前状态
  const container = document.querySelector('.PublicSentiment')
  if (container) {
    const scrollTop = container.scrollTop
    sessionStorage.setItem('publicSentiment_scroll', scrollTop.toString())
    console.log('点击详情前保存滚动位置:', scrollTop)
  }

  // 设置返回标记
  sessionStorage.setItem('publicSentiment_fromDetail', 'true')

  // 使用路由跳转到详情页
  router.push({
    path: '/PublicSentiment/detail',
    query: {
      url: itemInfo.url,
      title: itemInfo.title || '详情页'
    }
  })
}
```

#### 外链模式

```typescript
const openURL = () => {
  // 保存当前滚动位置到localStorage（外链跳转用）
  const container = document.querySelector('.PublicSentiment')
  if (container) {
    const scrollTop = container.scrollTop
    localStorage.setItem('publicSentiment_scroll', scrollTop.toString())
    console.log('点击详情前保存滚动位置:', scrollTop)
  }

  // 直接跳转到外链
  window.location.href = itemInfo.url
}
```

## 配置选项

```typescript
interface ScrollPositionOptions {
  pageKey: string                                                    // 页面唯一标识，必填
  containerSelector?: string                                         // 滚动容器选择器，可选
  supportTabs?: boolean                                             // 是否支持tab切换，默认false
  activeTab?: string                                                // 当前激活的tab名称（支持tab时必传）
  navigationMode?: 'newWindow' | 'currentPage' | 'externalLink'     // 跳转模式，默认'newWindow'
  saveDelay?: number                                                // 保存滚动位置的延迟时间（毫秒），默认1000
  restoreDelay?: number                                             // 恢复滚动位置的延迟时间（毫秒），默认100
  debug?: boolean                                                   // 是否启用调试日志，默认false
}
```

## 返回的方法和属性

```typescript
return {
  // 状态
  isRestoring,              // 是否正在恢复滚动位置
  currentActiveTab,         // 当前激活的tab
  
  // 方法
  init,                     // 初始化
  destroy,                  // 销毁
  saveScrollAndData,        // 保存滚动位置和数据
  restoreScrollPosition,    // 恢复滚动位置
  restorePageData,          // 恢复页面数据
  clearStoredData,          // 清除存储的数据
  beforeOpenDetail,         // 点击详情前调用
  onTabChange,              // tab切换时调用
  updateActiveTab,          // 更新当前activeTab
  
  // 生命周期辅助
  onMounted: init,          // 等同于init
  onBeforeUnmount: destroy  // 等同于destroy
}
```

## 工作原理

### 新窗口模式 (newWindow)
1. **页面可见性监听**：监听 `visibilitychange` 事件，当页面失去焦点时保存数据，重新获得焦点时恢复滚动位置
2. **滚动监听**：监听容器滚动事件，定期保存滚动位置
3. **焦点恢复**：当用户关闭新窗口回到原页面时，自动恢复滚动位置

### 当前页模式 (currentPage)
1. **路由监听**：监听 `popstate` 事件，检测用户通过浏览器后退按钮返回
2. **返回标记**：通过 `sessionStorage` 设置返回标记，区分是否从详情页返回
3. **状态恢复**：检测到从详情页返回时，自动恢复滚动位置和数据状态

### 外链模式 (externalLink)
1. **localStorage存储**：使用 `localStorage` 而不是 `sessionStorage` 保存数据，确保跨域跳转后数据不丢失
2. **页面重载恢复**：外链跳转后返回时页面会重新加载，在页面加载时检查并恢复状态
3. **长期缓存**：数据缓存时间延长到24小时，适应外链浏览的使用场景

### 通用机制
1. **滚动监听**：监听容器滚动事件，定期保存滚动位置
2. **数据持久化**：使用 `sessionStorage` 保存页面数据和滚动位置
3. **tab支持**：为每个tab生成独立的存储key，实现tab间的数据隔离
4. **数据过期**：保存的数据带有时间戳，1小时后自动过期

## 注意事项

1. 确保容器选择器正确，函数会自动查找合适的滚动容器
2. 在数据加载完成后调用 `saveScrollAndData` 保存状态
3. 支持tab的页面需要在tab切换时调用相应的方法
4. 调试模式下会输出详细的日志信息，生产环境建议关闭

## 已应用的页面

- ✅ 涉济舆情页面 (`/PublicSentiment`) - 外链跳转模式
- ✅ 热搜榜单页面 (`/hotList`) - 外链跳转模式，支持tab切换

## 路由配置

为了支持当前页跳转模式，需要添加相应的详情页路由：

```typescript
// router/index.ts
{
  path: '/PublicSentiment/detail',
  name: 'PublicSentimentDetail',
  component: () => import('@/components/urlIfram/index.vue')
},
{
  path: '/hotList/detail',
  name: 'HotListDetail',
  component: () => import('@/components/urlIfram/index.vue')
}
```

## 扩展到其他页面

要在其他页面使用此功能，只需要：

1. 导入并初始化 `useScrollPosition`
2. 在 `onMounted` 中恢复数据和滚动位置
3. 在数据加载完成后保存状态
4. 在 `onBeforeUnmount` 中保存最终状态
5. 在列表项点击时保存当前滚动位置
