<template>
  <div class="headerSearch flex">
    <!-- <div class="title">舆情提示单</div> -->
    <div class="search flex">
      <div v-if="keyword" class="keyword" @click="toSearchHistory">
        {{ keyword }}
      </div>
      <div v-else class="placeholder" @click="toSearchHistory">
        请输入关键词
      </div>
      <van-icon name="close" @click="clear" v-if="keyword !== ''" />
      <van-icon name="search" @click="search" />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, nextTick } from "vue";
import { useRoute, useRouter } from "vue-router";

const keyword = ref("");
const router = useRouter();
const route = useRoute();

const emit = defineEmits(["search"]);

const search = () => {
  emit("search");
};

const clear = () => {
  keyword.value = "";
  router.replace({
    query: {}, // 设置查询参数
  });
  setTimeout(() => {
    emit("search");
  });
};

const toSearchHistory = () => {
  router.push({
    path: "/SearchHistory",
    query: {
      source: route.path,
      keyword: keyword.value,
    },
  });
};
onMounted(() => {
  if (route.query.keyword) {
    keyword.value = route.query.keyword as string;
  }
});
</script>

<style lang="less" scoped>
.headerSearch {
  // background-color: #63adf8;
  // height: 80px;
  // justify-content: center;
  // flex-wrap: wrap;
  // position: relative;

  .title {
    width: 100%;
    color: #fff;
    font-weight: 600;
    line-height: 80px;
    font-size: 24px;
    text-align: center;
  }

  .search {
    background-color: #fff;
    // position: absolute;
    // bottom: -20px;
    margin-top: 20px;

    height: 40px;
    // width: 300px;
    // border-radius: 20px;
    width: 100%;
    border-bottom: 2px solid #63adf8;
    padding: 0 20px;
    justify-content: space-between;
    align-items: center;
    .placeholder,
    .keyword {
      flex: 1;
    }

    .van-icon-search {
      font-size: 30px;
      color: #999;
    }
    .van-icon-close {
      font-size: 20px;
      color: #999;
      margin-right: 10px;
    }

    .placeholder {
      line-height: 40px;
      color: #999;
    }
  }
}
</style>
