<template>
    <div class="urlContent">
        <iframe :src="urls" frameborder="0"></iframe>
    </div>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick } from "vue";
import { useRoute, useRouter } from "vue-router";
const { url} = defineProps(["url"]);
const router = useRouter();
const route = useRoute();
const urls = ref("");

 if (route.query.keyword) {
    urls.value = route.query.url as string;
  }
</script>
<style lang="less" scoped>
.urlContent {
    height: calc(~"100vh - 50px");
    overflow-y: auto;
}
</style>