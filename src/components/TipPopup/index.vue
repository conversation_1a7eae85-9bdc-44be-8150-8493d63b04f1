<!-- 自定义tip浮层组件 -->
<template>
  <div class="tip-popup-overlay" v-if="visible" @click="handleOverlayClick">
    <div class="tip-popup" :style="popupStyle">
      <!-- 箭头 -->
      <div class="tip-arrow" :class="arrowClass"></div>
      
      <!-- 内容区域 -->
      <div class="tip-content">
        <div 
          v-for="(action, index) in actions" 
          :key="index"
          class="tip-item"
          @click="handleItemClick(action)"
        >
          <div class="tip-icon">
            
            <SvgIcon v-if="action.icon" :name="action.icon" width="20" height="20" />
             <img v-if="action.img" style="width: 21px;height: auto;padding-bottom: 3px;" src="@/assets/img/lxyq_logo.png"  alt="">
          </div>
          <span class="tip-text">{{ action.text }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, nextTick } from 'vue';
import SvgIcon from '@/components/SvgIcon/index.vue';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  actions: {
    type: Array,
    default: () => []
  },
  position: {
    type: Object,
    default: () => ({ x: 0, y: 0 })
  },
  placement: {
    type: String,
    default: 'bottom-right' // top-left, top-right, bottom-left, bottom-right
  }
});

const emit = defineEmits(['update:visible', 'select']);

const popupStyle = computed(() => {
  const { x, y } = props.position;
  const placement = props.placement;
  
  let style = {
    position: 'absolute',
    zIndex: 1000
  };
  
  // 根据placement调整位置
  switch (placement) {
    case 'top-right':
      style.right = `${window.innerWidth - x}px`;
      style.bottom = `${window.innerHeight - y + 10}px`;
      break;
    case 'top-left':
      style.left = `${x}px`;
      style.bottom = `${window.innerHeight - y + 10}px`;
      break;
    case 'bottom-left':
      style.left = `${x}px`;
      style.top = `${y + 10}px`;
      break;
    case 'bottom-right':
    default:
      style.right = `${window.innerWidth - x}px`;
      style.top = `${y + 10}px`;
      break;
  }
  
  return style;
});

const arrowClass = computed(() => {
  return `arrow-${props.placement}`;
});

const handleOverlayClick = (e) => {
  if (e.target.classList.contains('tip-popup-overlay')) {
    emit('update:visible', false);
  }
};

const handleItemClick = (action) => {
  emit('select', action);
  emit('update:visible', false);
};
</script>

<style lang="less" scoped>
.tip-popup-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 999;
  background: transparent;
}

.tip-popup {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  min-width: 140px;
  overflow: hidden;
  
  .tip-arrow {
    position: absolute;
    width: 0;
    height: 0;
    border: 6px solid transparent;
    
    &.arrow-top-right,
    &.arrow-top-left {
      bottom: -12px;
      border-top-color: #fff;
      border-bottom: none;
    }
    
    &.arrow-bottom-right,
    &.arrow-bottom-left {
      top: -12px;
      border-bottom-color: #fff;
      border-top: none;
    }
    
    &.arrow-top-right,
    &.arrow-bottom-right {
      right: 20px;
    }
    
    &.arrow-top-left,
    &.arrow-bottom-left {
      left: 20px;
    }
  }
  
  .tip-content {
    padding: 8px 0;
  }
  
  .tip-item {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    cursor: pointer;
    transition: background-color 0.2s;
    
    &:hover {
      background-color: #f5f5f5;
    }
    
    &:active {
      background-color: #e8e8e8;
    }
    
    .tip-icon {
      margin-right: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    
    .tip-text {
      font-size: 14px;
      color: #333;
      white-space: nowrap;
    }
  }
}
</style>
