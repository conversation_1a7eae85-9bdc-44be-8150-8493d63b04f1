<!--  -->
<template>
  <div class="infoContent">
    <div class="title ellipsis-2" @click="titleClick">
      <van-highlight
        :keywords="[route.query.keyword? String(route.query.keyword) : '']"
        :source-string="info.msgTitle || ''"
        highlight-class="highlight0"
      />
    </div>
    <div class="abstract" @click="titleClick">
      <van-highlight
        :keywords="[route.query.keyword? String(route.query.keyword) : '']"
        :source-string="info.msgAbstract || ''"
        highlight-class="highlight0"
      />
    </div>
    <div class="url" @click="openURL">
      {{ info.msgContentUrl }}
       <SvgIcon
        width="30"
        height="30"
        name="已删除"
        class="tips"
        v-if="info.showDelTip && info.isMsgDel === 1"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted } from "vue";
import { useRoute, useRouter} from "vue-router";
import {  getLog } from "@/services/api";

const router = useRouter();
const route = useRoute();
interface Info {
  msgTitle: string;
  msgAbstract: string;
  msgContentUrl: string;
}

const emit = defineEmits(["on-click"]);
// 声明 props 类型
const props = defineProps(["info"]);
// 访问 props
const { info } = props;
const openURL = () => {
  getLog("舆情提示", "提示单原文");
  // window.location.href = info.msgContentUrl
  window.open(info.msgContentUrl, "_blank");
//  router.push({
//     path: "/externalUrl",
//     query: {
//       url: info.msgContentUrl,
//     },
//   });

};
const titleClick = () => {
  emit("on-click");
};
onMounted(() => {});
</script>
<style lang="less" scoped>
/* @import url(); 引入css类 */
.infoContent {
  font-size: 14px;
  text-align: left;
  & > div {
    word-break: break-all;
  }
  .title {
    line-height: 18px;
    font-size: 14px;
    font-weight: bold !important;
  }
  .abstract {
    line-height: 18px;
    margin: 10px 0;
  }
  .url {
    text-decoration: underline;
    color: #1873d7;
  }
  .tips{
    vertical-align: middle;
    transform-origin: center;
    transform: rotate(-15deg);
    margin-left: 5px;
  }
}
</style>
