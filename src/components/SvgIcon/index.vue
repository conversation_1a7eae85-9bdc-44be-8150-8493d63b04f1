<template>
  <svg :style="{ width: viewportWidth, height: viewportHeight }">
    <use :xlink:href="prefix + name" :fill="color"></use>
  </svg>
</template>

<script setup lang="ts">
import { computed } from "vue";

const props = defineProps({
  //xlink:href属性值的前缀
  prefix: {
    type: String,
    default: "#icon-",
  },
  //svg矢量图的名字
  name: String,
  //svg图标的颜色
  color: {
    type: String,
    default: "",
  },
  //svg宽度
  width: {
    type: String,
    default: "16px",
  },
  //svg高度
  height: {
    type: String,
    default: "16px",
  },
});

// 计算响应式宽度（转换为 vw 单位）
const viewportWidth = computed(() => {
  const numericWidth = parseFloat(props.width); // 提取数值部分
  return isNaN(numericWidth) ? "16px" : `${(numericWidth / 375) * 100}vw`; // 转换为 vw 单位
});

// 计算响应式高度（转换为 vw 单位）
const viewportHeight = computed(() => {
  const numericHeight = parseFloat(props.height); // 提取数值部分
  return isNaN(numericHeight) ? "16px" : `${(numericHeight / 375) * 100}vw`; // 转换为 vw 单位
});
</script>
<style scoped></style>
