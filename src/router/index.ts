import { createRouter, createWebHistory } from 'vue-router'
import { stringify, parse } from 'flatted';
const router = createRouter({
    history: createWebHistory(import.meta.env.BASE_URL),
    routes: [
        {
            path: '/',
            name: 'Home',
            component: () => import('@/views/home/<USER>')
        },
        {
            path: '/externalUrl',
            name: 'externalUrl',
            component: () => import('@/components/urlIfram/index.vue')
        },
        {
            path: '/PublicSentiment',
            name: 'PublicSentiment',
            component: () => import('@/views/PublicSentiment/index.vue'),
        },
        {
            path: '/PublicSentiment/detail',
            name: 'PublicSentimentDetail',
            component: () => import('@/components/urlIfram/index.vue')
        },
        {
            path: '/PromptSheet',
            name: 'PromptSheet',
            component: () => import('@/views/promptSheet/index.vue'),
            redirect: '/PromptSheet/TodoList',
            children: [
                {
                    path: '/PromptSheet/TodoList',
                    name: 'TodoList',
                    component: () => import('@/views/todoList/index.vue')
                },
                {
                    path: '/PromptSheet/NearlyWeek',
                    name: 'NearlyWeek',
                    component: () => import('@/views/nearlyWeek/index.vue')
                },
            ]
        },
        {
            path: '/SearchHistory',
            name: 'SearchHistory',
            component: () => import('@/views/searchHistory/index.vue')
        }, {
            path: '/Details/PromptSheet',
            name: 'promptSheet',
            component: () => import('@/views/details/promptSheet.vue')
        }, {
            path: '/Details/FillFeedback',
            name: 'FillFeedback',
            component: () => import('@/views/details/fillFeedback.vue')
        },
        {
            path: '/hotList',
            name: '热搜榜单',
            component: () => import('@/views/hotList/index.vue'),
        },
        {
            path: '/hotList/detail',
            name: 'HotListDetail',
            component: () => import('@/components/urlIfram/index.vue')
        },
        {
            path: '/eventAnalysis',
            name: '舆情事件',
            component: () => import('@/views/eventAnalysis/index.vue'),
        },
        {
            path: '/eventAnalysis/Details',
            name: '事件分析',
            component: () => import('@/views/eventAnalysis/components/detail/index.vue'),
        },
        {
            path: '/RefuteRumour',
            name: '辟谣信息',
            component: () => import('@/views/RefuteRumour/index.vue')
        },
        {
            path: '/FeedBack',
            name: '意见反馈',
            component: () => import('@/views/FeedBack/index.vue')
        },
        {
            path: '/NetInfoDynamic',
            name: '网信动态',
            component: () => import('@/views/NetInfoDynamic/index.vue'),
            redirect: '/NetInfoDynamic/CityDynamic',
            children: [
                {
                    path: 'CityDynamic',
                    name: '全市动态',
                    component: () => import('@/views/NetInfoDynamic/cityDynamic.vue')
                },
                {
                    path: 'DistrictDynamic',
                    name: '区县动态',
                    component: () => import('@/views/NetInfoDynamic/districtDynamic.vue')
                }
            ]
        },
        {
            path: '/NetInfoDynamic/detail',
            name: '网信动态详情',
            component: () => import('@/views/NetInfoDynamic/detail.vue')
        }

    ]
})
router.beforeEach((to, from, next) => {
    if (from.name) {
        let json = stringify(from)
        localStorage.setItem('routerFrom', json)
    }
    next();
});
export default router
