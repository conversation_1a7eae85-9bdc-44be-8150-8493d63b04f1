<template>
  <div class="NearlyWeek">
    <!-- <HeaderSearch @search="search" /> -->
    <div class="listBox">
      <van-empty v-if="!loading && count === 0" description="暂无数据" />
       <van-pull-refresh v-model="refreshing" @refresh="onRefresh" v-else>
      <van-list
        v-model:loading="loading"
        :finished="finished"
        :finished-text="`共 ${count} 个提示`"
        loading-text="加载中..."
        @load="getData"
      >
        <ListItem v-for="item in dataList" :itemInfo="item" />
      </van-list>
      <van-back-top bottom="10vh" right="5vw" />
       </van-pull-refresh>
    </div>
  </div>
</template>

<script lang="ts" setup>
import moment from "moment";
import HeaderSearch from "@/components/headerSearch/index.vue";
import ListItem from "./components/listItem/index.vue";
import { onMounted, reactive, ref } from "vue";
import { findAllAPI, findAll} from "@/services/api";
import { useRoute } from "vue-router";
const route = useRoute();
const pageNo = ref(0);
const count = ref(0);
const loading = ref(false);
const finished = ref(false);
const refreshing = ref(false);
let dataList = reactive<any[]>([]);

// 获取今天的日期
const today = moment();

// 获取最近七天的日期
const lastSevenDays = Array.from({ length: 7 }, (_, i) =>
  moment(today).subtract(i, "days").format("YYYY-MM-DD")
);

// 显示最近七天的日期
console.log("最近七天的日期：", lastSevenDays);
const search = () => {
  count.value = 0;
  loading.value = true;
  dataList.length = 0;
  pageNo.value = 0;
  getData();
};

const getData = async () => {
  loading.value = true;
  pageNo.value++;
  let organType = localStorage.getItem("organType");

  setTimeout(() => {
    if (refreshing.value) {
      dataList.length = 0;
      refreshing.value = false;
    }
  });
  let response;
  if (organType == "1") {
    const params = {
      timeType: 90,
      status: -1,
      keyWord: route.query.keyword,
      pageNum: pageNo.value,
      pageSize: 20,
      organId: localStorage.getItem("organId"),
      orderType: "desc",
      orderBy: "send_time",
    };
    response = await findAll(params);
  } else {
    const params = {
      timeType: 90,
      msgStatus: -1,
      msgType: 1,
      keyWord: route.query.keyword,
      pageNum: pageNo.value,
      pageSize: 20,
      organId: localStorage.getItem("organId"),
      orderType: "desc",
      orderBy: "send_time",
    };
    response = await findAllAPI(params);
  }
  let data = response.data;
  count.value = data.count;
  dataList.push(...data.data);
  loading.value = false;
  if (dataList.length >= count.value) {
    finished.value = true;
  }
};

const onRefresh = () => {
  pageNo.value = 0;
  finished.value = false;
  getData();
};

onMounted(() => {
  getData();
});
</script>
<style lang="less" scoped>
.NearlyWeek {
  height: calc(~"100vh");
  overflow-y: auto;
  .listBox {
    padding: 20px 0;
  }
}
</style>
