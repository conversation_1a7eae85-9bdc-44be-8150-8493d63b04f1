<!-- 列表单项 -->
<template>
  <div class="listItem flex">
    <div class="content">
      <div class="title ellipsis-2" @click="toDetails">
        {{ itemInfo.title }}
      </div>
      <div class="remark flex sb">
        <div class="ellipsis" style="flex: 1;">
          {{ itemInfo.source }}
        </div>
        <div>
          {{ handleTime(itemInfo.inputtime) }}
        </div>
      </div>
    </div>
    <div class="picture" v-if="itemInfo.thumb" @click="toDetails">
      <img :src="itemInfo.thumb" alt="" />
    </div>
  </div>
</template>

<script setup lang="ts">
//生命周期 - 创建完成（访问当前this实例）
import moment from "moment";

const { itemInfo } = defineProps(["itemInfo"]);

const toDetails = () => {
  window.open(itemInfo.url, "_blank");
};

const handleTime = (time: string) => {
  return moment(time).format("YYYY-MM-DD");
  // return time.substring(0, 10);
};
</script>
<style lang="less" scoped>
/* @import url(); 引入css类 */
.listItem {
  margin-bottom: 10px;
  background-color: #fff;
  padding: 10px;
  .content {
    flex: 1;
    .title {
      font-size: 14px;
      height: 28px;
      line-height: 14px;
    }
    .remark {
      width: 250px;
      margin-top: 10px;
      font-size: 12px;
      color: #999;
      line-height: 20px;
    }
  }
  .picture {
    img {
      height: 60px;
      width: 90px;
      object-fit: cover;
    }
  }
}
</style>
