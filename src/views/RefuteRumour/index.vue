<!-- 辟谣信息 -->
<template>
  <div>
    <div v-if="userAccount == 'wangxiaonan'">
    <van-tabs
      v-model:active="active"
      @click-tab="onClickTab"
      background="#f4f4f4"
    >
      <van-tab  key="index" title="推荐关注"> </van-tab>
      <RouterView />
    </van-tabs>
  </div>
  <div class="RefuteRumour">
    <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
      <van-empty
        v-if="!loading && dataList.length === 0"
        description="暂无数据"
      />
      <van-list
        v-else
        v-model:loading="loading"
        :finished="finished"
        finished-text="没有更多了"
        loading-text="加载中..."
        @load="getListData"
        offset="50"
      >
        <ListItem
          v-for="(item, index) in dataList"
          :itemInfo="item"
          :index="index"
          :key="item.id"
        />
      </van-list>
      <van-back-top bottom="10vh" right="5vw" />
    </van-pull-refresh>
  </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref, nextTick } from "vue";

import { getRefuteRumourList } from "@/services/api";
import ListItem from "./cpmonents/listItem/index.vue";
const loading = ref(false);
const finished = ref(true);
const refreshing = ref(false);
const pageNo = ref(0);
const dataList = ref<any[]>([]);
const active = ref(1);
const userAccount = localStorage.getItem('userAccount');


//生命周期 - 创建完成（访问当前this实例）

const getListData = async () => {
  console.log("获取数据");
  //   loading.value = true;
  //   finished.value = true;
  pageNo.value++;
  let params = {
    pageNum: pageNo.value,
    pageSize: 20,
  };
  const res = await getRefuteRumourList(params);
  console.log(res);
  
  dataList.value = dataList.value.concat(res.data);
  loading.value = false;
  finished.value = false;
  refreshing.value = false;
};
const onRefresh = () => {
  pageNo.value = 0;
  console.log("顶部刷新");
  getListData();
};

const onClickTab = () => {
  var url = 'https://jnpy.e23.cn/zhpysdt/sdt/loginBySdt?token=' + localStorage.getItem('token');
  console.log("url:" + url);
  window.open(url, "_blank");
};
onMounted(() => {
  getListData();
  nextTick(() => {
    document.title = "辟谣信息";
  });
});
</script>
<style lang="less" scoped>
/* @import url(); 引入css类 */
.RefuteRumour {
  overflow-y: auto;
  height: calc(~"100vh - 50px");
  .van-list {
  }
}
</style>

<style lang="less" scoped>
:deep(.van-tabs__content){
  position: relative;
  z-index: 0;
}
:deep(.van-tabs__wrap){
  position: relative;
  top: 0;
  z-index: 1;
}
:deep(.van-tab--active) {
  color: #0e70fd;
}
</style>

<style lang="less" scoped>
:deep(.van-tab--active) {
  color: #0e70fd;
}
.net-info-dynamic {
  height: calc(~"100vh - 50px");
  overflow-y: auto;
}
:deep(.van-tabs__wrap){
  position: sticky;
  top: 0;
  z-index: 999;
  background: #f4f4f4;
}
</style>
