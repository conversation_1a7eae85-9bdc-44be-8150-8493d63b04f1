<!-- 全市动态页面 -->
<template>
  <div class="city-dynamic">
    <van-empty v-if="!loading && count === 0" description="暂无数据" />
    <van-pull-refresh v-model="refreshing" @refresh="onRefresh" v-else>
      <van-list
        v-model:loading="loading"
        :finished="finished"
        finished-text="没有更多了"
        loading-text="加载中..."
        @load="getData"
      >
        <ListItem v-for="item in dataList" :itemInfo="item" :key="item.docid" />
      </van-list>
      <van-back-top bottom="10vh" right="5vw" @click="scrollToTop"/>
    </van-pull-refresh>
  </div>
</template>

<script setup>
import { onMounted, reactive, ref, nextTick, onBeforeUnmount } from "vue";
import { getWorkDynamic, getLog } from "@/services/api";
import { useRouter } from "vue-router";
import ListItem from "./components/dynamicList.vue";

const count = ref(0);
const loading = ref(false);
const finished = ref(false);
const refreshing = ref(false);
const pageNo = ref(0);
let dataList = reactive([]);

const router = useRouter();

// 滚动位置管理
const SCROLL_KEY = 'cityDynamic_scroll';
const DATA_KEY = 'cityDynamic_data';

// 自定义返回顶部方法
const scrollToTop = () => {
  const container = document.querySelector('.city-dynamic');
  const tabsWrap = document.querySelector('.van-tabs__wrap');
  
  if (container && tabsWrap) {
    const tabHeight = tabsWrap.offsetHeight;
    container.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
    
    // 由于tab是sticky定位，实际上不需要额外偏移
    // 如果还是被遮挡，可以尝试滚动到负值
    // container.scrollTop = -tabHeight;
  }
};

// 保存滚动位置和数据
const saveScrollAndData = () => {
  // 获取city-dynamic容器的滚动位置
  const container = document.querySelector('.city-dynamic');
  const scrollTop = container ? container.scrollTop : 0;
  sessionStorage.setItem(SCROLL_KEY, scrollTop.toString());

  const pageData = {
    dataList: [...dataList],
    pageNo: pageNo.value,
    finished: finished.value,
    count: count.value
  };
  sessionStorage.setItem(DATA_KEY, JSON.stringify(pageData));

  console.log('保存滚动位置和数据:', scrollTop, pageData);
};

// 恢复滚动位置
const restoreScrollPosition = () => {
  const savedScrollTop = sessionStorage.getItem(SCROLL_KEY);
  if (savedScrollTop) {
    const scrollTop = parseInt(savedScrollTop, 10);
    console.log('恢复滚动位置:', scrollTop);

    setTimeout(() => {
      const container = document.querySelector('.city-dynamic');
      if (container) {
        container.scrollTop = scrollTop;
        console.log('已设置容器滚动位置:', scrollTop);
      }
    }, 100);
  }
};

// 恢复页面数据
const restorePageData = () => {
  const savedData = sessionStorage.getItem(DATA_KEY);
  if (savedData) {
    try {
      const pageData = JSON.parse(savedData);
      if (pageData.dataList && pageData.dataList.length > 0) {
        console.log('恢复页面数据:', pageData);

        dataList.length = 0;
        dataList.push(...pageData.dataList);
        pageNo.value = pageData.pageNo || 0;
        finished.value = pageData.finished || false;
        count.value = pageData.count || 0;

        return true;
      }
    } catch (error) {
      console.error('恢复页面数据失败:', error);
    }
  }
  return false;
};

const getData = async () => {
  loading.value = true;
  pageNo.value++;
  
  setTimeout(() => {
    if (refreshing.value) {
      dataList.length = 0;
      refreshing.value = false;
    }
  });

  try {
    const response = await getWorkDynamic({
      pageNo: pageNo.value,
      pageSize: 20,
      docchannel: 4 // 全市动态
    });

    console.log("API响应:", response);

    if (response.status === 0) {
      const newData = response.data.list || [];
      console.log("原始数据:", newData);

      // 处理数据，转换字段名
      const processedData = newData.map((item) => ({
        ...item,
        title: item.doctitle,
        source: item.docsourcename || '未知来源',
        inputtime: item.docreltime,
        thumb: item.thumbfiles ? item.thumbfiles.split(',')[0] : null,
        url: item.docpuburl || '#',
        id: item.docid,
        docid: item.docid
      }));

      console.log("处理后数据:", processedData);
      dataList.push(...processedData);
      console.log("dataList:", dataList);

      // 更新count
      count.value = dataList.length;

      if (newData.length < 20) {
        finished.value = true;
      }
    }
  } catch (error) {
    console.error("获取全市动态数据失败:", error);
  } finally {
    loading.value = false;
  }
};

const onRefresh = () => {
  pageNo.value = 0;
  finished.value = false;

  // 刷新后确保滚动到正确位置，避免被tabs遮挡
  nextTick(() => {
    const container = document.querySelector('.city-dynamic');
    if (container) {
      container.scrollTop = 0;
    }
  });

  getData();
};

onMounted(() => {
  // 检查是否是从详情页返回
  const isFromDetail = sessionStorage.getItem('fromDetailPage');

  if (isFromDetail === 'cityDynamic') {
    console.log('从详情页返回，恢复数据和滚动位置');
    // 恢复页面数据
    const hasData = restorePageData();
    if (hasData) {
      // 恢复滚动位置
      restoreScrollPosition();
    } else {
      // 如果没有数据，重新加载
      getData();
      getLog("网信动态/全市动态", "浏览");
    }
    // 清除标记
    sessionStorage.removeItem('fromDetailPage');
  } else {
    // 正常加载
    getData();
    getLog("网信动态/全市动态", "浏览");
  }

  nextTick(() => {
    document.title = "网信动态";
  });

  // 设置滚动监听
  setupScrollListener();
});

// 页面卸载前保存数据
onBeforeUnmount(() => {
  saveScrollAndData();
  removeScrollListener();
});

// 监听容器滚动，定期保存位置
let scrollTimer = null;
const handleScroll = () => {
  if (scrollTimer) clearTimeout(scrollTimer);
  scrollTimer = setTimeout(() => {
    saveScrollAndData();
  }, 500);
};

// 添加滚动监听
const setupScrollListener = () => {
  nextTick(() => {
    const container = document.querySelector('.city-dynamic');
    if (container) {
      container.addEventListener('scroll', handleScroll);
      console.log('已添加容器滚动监听');
    }
  });
};

// 移除滚动监听
const removeScrollListener = () => {
  const container = document.querySelector('.city-dynamic');
  if (container) {
    container.removeEventListener('scroll', handleScroll);
    console.log('已移除容器滚动监听');
  }
  if (scrollTimer) clearTimeout(scrollTimer);
};
</script>

<style lang="less" scoped>
.city-dynamic {
  /* 减去 tabs 的高度，大约 44px，避免内容被遮挡 */
  height: calc(100vh - 44px);
  overflow-y: auto;
  /* 确保容器从正确位置开始 */
  position: relative;
}

/* 确保下拉刷新组件正确显示 */
:deep(.van-pull-refresh) {
  min-height: 100%;
}

/* 确保列表项不被遮挡 */
:deep(.van-list) {
  padding-top: 0;
}

/* 优化下拉刷新的显示效果 */
:deep(.van-pull-refresh__track) {
  min-height: 100%;
}
</style>
