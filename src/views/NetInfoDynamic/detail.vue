<!-- 网信动态详情页 -->
<template>
  <div class="dynamic-detail">
    <!-- <van-nav-bar
      title="网信动态详情"
      left-text="返回"
      left-arrow
      @click-left="onClickLeft"
    /> --> 
    
    <div class="detail-content" v-if="!loading && detailData">
      <div class="article-header">
        <h1 class="title">{{ detailData.doctitle }}</h1>
        <div class="meta-info">
          <span class="source">{{ detailData.docsourcename || '未知来源' }}</span>
        </div>
        <div class="meta-info" v-if="detailData.readcount">
          <span >{{ formatTime(detailData.docreltime) }}</span>
          <span >阅读次数：{{ detailData.readcount || '0' }}</span>
        </div>
      </div>
      
      <div class="article-body">
      
        <!-- <div v-if="detailData.thumbfiles" class="article-images">
          <img 
            v-for="(img, index) in getImages(detailData.thumbfiles)" 
            :key="index"
            :src="img" 
            alt="文章图片"
            @click="previewImage(img, getImages(detailData.thumbfiles))"
          />
        </div> -->
        
        <div class="content" v-html="detailData.dochtmlcon"></div>
      </div>
    </div>
    
    <van-loading v-if="loading" class="loading-center" size="24px" vertical>
      加载中...
    </van-loading>
    
    <van-empty v-if="!loading && !detailData" description="暂无数据" />
    
    <van-back-top bottom="10vh" right="5vw" />
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { getWorkDynamicDetail, getLog } from '@/services/api';
import moment from 'moment';
import { showImagePreview } from 'vant';

const route = useRoute();
const router = useRouter();

const loading = ref(true);
const detailData = ref<any>(null);

const onClickLeft = () => {
  router.back();
};

const formatTime = (time: string) => {
  return moment(time).format('YYYY-MM-DD');
};

const getImages = (thumbfiles: string) => {
  if (!thumbfiles) return [];
  return thumbfiles.split(',').filter(img => img.trim());
};

const previewImage = (current: string, images: string[]) => {
  showImagePreview({
    images,
    startPosition: images.indexOf(current),
    closeable: true
  });
};

const getDetailData = async () => {
  const docid = route.query.docid;
  if (!docid) {
    console.error('缺少docid参数');
    loading.value = false;
    return;
  }

  try {
    const response: any = await getWorkDynamicDetail({ docid });
    if (response.status === 0) {
      detailData.value = response.data;
    }
  } catch (error) {
    console.error('获取详情失败:', error);
  } finally {
    loading.value = false;
  }
};

onMounted(() => {
  getLog('网信动态/详情', '浏览');
  getDetailData();
});
</script>

<style lang="less" scoped>
/deep/.van-tabbar--fixed{
  z-index:10;
}
.dynamic-detail {
  min-height: 100vh;
  background: #f5f5f5;
}

.detail-content {
  background: #fff;
  margin: 10px;
  border-radius: 8px;
  overflow: hidden;
  position: relative;
  &:after{
    content:"";
    display:block;
    position: absolute;
    top:0;
    bottom:0;
    left:0;
    right:0;
  
    z-index: 10;
  }
}

.article-header {
  padding: 20px 20px 10px 20px;
  border-bottom: 1px solid #f0f0f0;
  
  .title {
    font-size: 18px;
    font-weight: bold;
    line-height: 1.4;
    margin: 0 0 15px 0;
    color: #333;
  }
  
  
}
.meta-info {
    margin:3px 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 12px;
    color: #999;
    
    .source {
      flex: 1;
    }
    
    .time {
      margin-left: 10px;
      font-size: 12px;
    color: #999;
    }
  }

.article-body {
  padding: 20px;
  
  .article-images {
    margin-bottom: 20px;
    
    img {
      width: 100%;
      max-width: 100%;
      height: auto;
      border-radius: 4px;
      margin-bottom: 10px;
      cursor: pointer;
      
      &:last-child {
        margin-bottom: 0;
      }
    }
  }
  
  .content {
    font-size: 16px;
    line-height: 1.6;
    color: #333;
    padding-bottom:50px;
    :deep(img) {
      display:block;
      width: 100%!important;
      max-width: 100%!important;
      height: auto!important;
      border-radius: 4px;
      margin: 10px 0;
    }
    
    :deep(p) {
      margin: 10px 0;
    }
  }
}

.loading-center {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
</style>
