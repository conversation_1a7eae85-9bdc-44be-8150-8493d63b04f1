<!-- 网信动态列表单项 -->
<template>
  <div class="listItem flex">
    <div class="content">
      <div class="title ellipsis-2" @click="toDetails">
        {{ itemInfo.title || '无标题' }}
      </div>
      <div class="remark flex sb">
        <div class="ellipsis" style="flex: 1;">
          {{ itemInfo.source || '无来源' }}
        </div>
        <div>
          {{ handleTime(itemInfo.inputtime) || '无时间' }}
        </div>
      </div>
    </div>
    <div class="picture" v-if="itemInfo.thumb" @click="toDetails">
      <img :src="'/images/' + itemInfo.thumb" alt="" />
    </div>
  </div>
</template>

<script setup>
import moment from "moment";
import { useRouter, useRoute } from "vue-router";
import { getLog, addReadCount } from "@/services/api";

const { itemInfo } = defineProps(["itemInfo"]);
const router = useRouter();
const route = useRoute();

const toDetails = async () => {
  getLog("网信动态/详情", "浏览");

  // 根据当前路由设置返回标记
  if (route.path.includes('CityDynamic')) {
    sessionStorage.setItem('fromDetailPage', 'cityDynamic');
  } else if (route.path.includes('DistrictDynamic')) {
    sessionStorage.setItem('fromDetailPage', 'districtDynamic');
  }

  // 调用阅读数统计接口
  try {
    const docid = itemInfo.docid || "";
    const docchannel=itemInfo.docchannel ||"";
    const siteid = itemInfo.siteid;
    console.log('点击列表项，添加阅读数统计:', { docid, docchannel, siteid: siteid });

    const result = await addReadCount(docid, docchannel, siteid);
    if (result.status === 0) {
      console.log('阅读数统计成功:', result.message);
    } else {
      console.warn('阅读数统计失败:', result.message);
    }
  } catch (error) {
    console.error('阅读数统计接口调用失败:', error);
  }

  // 跳转到详情页，传递docid参数
  router.push({
    path: '/NetInfoDynamic/detail',
    query: { docid: itemInfo.docid || itemInfo.id }
  });
};

const handleTime = (time) => {
  if (!time) return '';
  return moment(time).format("YYYY-MM-DD");
};
</script>

<style lang="less" scoped>
.listItem {
  margin-bottom: 10px;
  background-color: #fff;
  padding: 10px;
  .content {
    flex: 1;
    .title {
      font-size: 14px;
      height: 28px;
      line-height: 14px;
    }
    .remark {
      width: 250px;
      margin-top: 10px;
      font-size: 12px;
      color: #999;
      line-height: 20px;
    }
  }
  .picture {
    img {
      height: 60px;
      width: 90px;
      object-fit: cover;
    }
  }
}
</style>
