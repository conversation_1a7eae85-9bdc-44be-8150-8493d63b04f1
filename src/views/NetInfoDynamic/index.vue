<!-- 网信动态主页面 -->
<template>
  <div>
    <van-tabs
      v-model:active="active"
      @click-tab="onClickTab"
      background="#f4f4f4"
    >
      <van-tab v-for="(itm, index) in navLists" :key="index" :title="itm"> </van-tab>
      <RouterView />
    </van-tabs>
  </div>
</template>

<script lang="ts" setup>
import { RouterView, useRoute, useRouter } from "vue-router";
import { onMounted, reactive, ref } from "vue";
import { getLog } from "@/services/api";

const router = useRouter();
const route = useRoute();
const active = ref(0);
let navLists = reactive<any[]>(["全市动态", "区县动态"]);

const onClickTab = ({ title }: { title: any }) => {
  let path = "";
  if (title == "全市动态") {
    path = "/NetInfoDynamic/CityDynamic";
  } else {
    path = "/NetInfoDynamic/DistrictDynamic";
    getLog("网信动态", "区县动态浏览");
  }

  router.push({
    path: path,
  });
};

onMounted(() => {
  if (route.path.indexOf("/NetInfoDynamic/DistrictDynamic") > -1) {
    active.value = 1;
  }
  getLog("网信动态", "浏览");
});
</script>

<style lang="less" scoped>
:deep(.van-tabs__content){
  position: relative;
  z-index: 0;
}
:deep(.van-tabs__wrap){
  position: relative;
  top: 0;
  z-index: 1;
}
:deep(.van-tab--active) {
  color: #0e70fd;
}
</style>

<style lang="less" scoped>
:deep(.van-tab--active) {
  color: #0e70fd;
}
.net-info-dynamic {
  height: calc(~"100vh - 50px");
  overflow-y: auto;
}
:deep(.van-tabs__wrap){
  position: sticky;
  top: 0;
  z-index: 999;
  background: #f4f4f4;
}
</style>
