<!-- 涉济舆情 -->
<template>
  <div class="PublicSentiment">
    <van-sticky>
      <van-search
        v-model="keyword"
        @search="handleSearch"
        placeholder="请输入搜索关键词"
      />
    </van-sticky>
    <div class="list">
      <van-empty v-if="!loading && count === 0" description="暂无数据" />
      <van-list
        v-else
        v-model:loading="loading"
        :finished="finished"
        loading-text="加载中..."
        @load="getListData"
      >
        <ListItem v-for="item in dataList" :key="item.mkey || item.id" :itemInfo="item" />
      </van-list>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, onBeforeUnmount } from "vue";
import ListItem from "./components/listItem/index.vue";
import {
  getJNPublicSentimentCount,
  getJNPublicSentimentList,
  getLog,
} from "@/services/api";

import { ref, nextTick } from "vue";
import { useScrollPosition } from "@/composables/useScrollPosition";

const loading = ref(false);
const finished = ref(false);
const count = ref(0);
let dataList = ref<any[]>([]);
const keyword = ref("");
const pageNo = ref(0);

// 使用滚动位置保持功能，针对外链跳转优化
const scrollPosition = useScrollPosition({
  pageKey: 'publicSentiment',
  containerSelector: '.PublicSentiment',
  navigationMode: 'externalLink', // 外链跳转模式
  debug: true
});
const getCount = () => {
  pageNo.value = 0;
  let params = {
    dayNum: 3,
    orderByType: 1,
    timeType: 1,
    topic: 3,
    keyword: keyword.value,
  };
  loading.value = true;
  dataList.value.length = 0;
  getJNPublicSentimentCount(params).then((res) => {
    if (res.data === 0) {
      loading.value = false;
      count.value = 0;
    } else {
      count.value = res.data;
      getListData();
    }
  });
};
const getListData = () => {
  pageNo.value++;
  let params = {
    dayNum: 3,
    orderByType: 1,
    timeType: 2,
    topic: 3,
    pageNo: pageNo.value,
    pageSize: 50,
    keyword: keyword.value,
  };
  getJNPublicSentimentList(params).then((res) => {
    dataList.value = dataList.value.concat(res.data);
    if (dataList.value.length >= count.value) {
      finished.value = true;
    }
    loading.value = false;

    // 保存数据状态
    scrollPosition.saveScrollAndData({
      dataList: dataList.value,
      pageNo: pageNo.value,
      finished: finished.value,
      count: count.value,
      keyword: keyword.value
    });
  });
};
const handleSearch = () => {
  finished.value = false;
  pageNo.value = 0;
  dataList.value = [];
  getCount();
};
onMounted(() => {
  // 尝试恢复页面数据
  const savedData = scrollPosition.restorePageData();
  if (savedData) {
    // 恢复数据
    dataList.value = savedData.dataList || [];
    pageNo.value = savedData.pageNo || 0;
    finished.value = savedData.finished || false;
    count.value = savedData.count || 0;
    keyword.value = savedData.keyword || '';

    // 恢复滚动位置
    scrollPosition.restoreScrollPosition();
  } else {
    // 如果没有数据，正常加载
    getCount();
  }

  getLog("涉济舆情/首页", "浏览");

  nextTick(() => {
    document.title = "涉济舆情";
  });

  // 初始化滚动位置管理
  scrollPosition.onMounted();
});

// 页面卸载前保存数据
onBeforeUnmount(() => {
  scrollPosition.saveScrollAndData({
    dataList: dataList.value,
    pageNo: pageNo.value,
    finished: finished.value,
    count: count.value,
    keyword: keyword.value
  });
  scrollPosition.onBeforeUnmount();
});
</script>
<style lang="less" scoped>
/* @import url(); 引入css类 */
.PublicSentiment {
  height: calc(~"100vh");
  overflow-y: auto;
  .list {
    height: 100%;
  }
}
</style>
