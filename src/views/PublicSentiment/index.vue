<!-- 涉济舆情 -->
<template>
  <div class="PublicSentiment">
    <van-sticky>
      <van-search
        v-model="keyword"
        @search="handleSearch"
        placeholder="请输入搜索关键词"
      />
    </van-sticky>
    <div class="list">
      <van-empty v-if="!loading && count === 0" description="暂无数据" />
      <van-list
        v-else
        v-model:loading="loading"
        :finished="finished"
        loading-text="加载中..."
        @load="getListData"
      >
        <ListItem v-for="item in dataList" :key="item.mkey || item.id" :itemInfo="item" />
      </van-list>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, onBeforeUnmount } from "vue";
import ListItem from "./components/listItem/index.vue";
import {
  getJNPublicSentimentCount,
  getJNPublicSentimentList,
  getLog,
} from "@/services/api";

import { ref, nextTick } from "vue";
import { useScrollPosition } from "@/composables/useScrollPosition";

const loading = ref(false);
const finished = ref(false);
const count = ref(0);
let dataList = ref<any[]>([]);
const keyword = ref("");
const pageNo = ref(0);

// 使用滚动位置保持功能，针对外链跳转优化
const scrollPosition = useScrollPosition({
  pageKey: 'publicSentiment',
  containerSelector: '.PublicSentiment',
  navigationMode: 'externalLink', // 外链跳转模式
  debug: true
});
const getCount = () => {
  pageNo.value = 0;
  let params = {
    dayNum: 3,
    orderByType: 1,
    timeType: 1,
    topic: 3,
    keyword: keyword.value,
  };
  loading.value = true;
  dataList.value.length = 0;
  getJNPublicSentimentCount(params).then((res) => {
    if (res.data === 0) {
      loading.value = false;
      count.value = 0;
    } else {
      count.value = res.data;
      getListData();
    }
  });
};
const getListData = () => {
  pageNo.value++;
  let params = {
    dayNum: 3,
    orderByType: 1,
    timeType: 2,
    topic: 3,
    pageNo: pageNo.value,
    pageSize: 50,
    keyword: keyword.value,
  };
  getJNPublicSentimentList(params).then((res) => {
    //假数据haha
    // res.data=[
    //     {
    //         "id": 11404311,
    //         "mkey": "202508y89B94D6F54C7A4A082552CFDE42D3D17",
    //         "mtitle": "济南主播公司竟对未成年下此狠手",
    //         "mabstract": "济南主播公司竟对未成年下此狠手&nbsp。",
    //         "murl": "http://www.xiaohongshu.com/discovery/item/68aed686000000001d0085ec",
    //         "msentiment": -1,
    //         "mwebsiteName": "小红书",
    //         "uname": "瓜社速递",
    //         "ukey": "",
    //         "situation": 80,
    //         "readCnt": 0,
    //         "replyCnt": 0,
    //         "replyForwardCnt": 0,
    //         "likeCnt": 0,
    //         "marea": "[\"济南市\"]",
    //         "scopeArea": "[\"涉法涉诉其他\"]",
    //         "status": 0,
    //         "type": 1,
    //         "mark": 1,
    //         "mpublishTime": 1756288646000,
    //         "insertTime": 1756299960000,
    //         "isImportant": 0,
    //         "titleMd5": "191158e8eca5335b",
    //         "topicNum": 0,
    //         "reportId": "#259#",
    //         "reportName": "#智慧星光#",
    //         "reportUserId": "#-3#",
    //         "reportUserName": "#系统账号#",
    //         "isWorkday": 1,
    //         "isValid": 1,
    //         "updateTime": 1756300213246,
    //         "reportTime": "#2025-08-27 21:06#",
    //         "isEarlyWarning": null,
    //         "countyOrganId": null,
    //         "isCounty": 0,
    //         "promptNum": null,
    //         "insertTime2": 1756300213237,
    //         "isMonitor": null,
    //         "monitorName": null,
    //         "isEvidence": null,
    //         "isMaterial": null,
    //         "isRead": 0,
    //         "remark": null,
    //         "mAreaIssue": null,
    //         "mLocIssue": null,
    //         "sendMsg": 2,
    //         "isMsgDel": 1,
    //         "isVideo": null,
    //         "isForward": null,
    //         "isUpdateMab": null,
    //         "isNotVideo": null,
    //         "videoStatus": null,
    //         "reportOrgUserName": "智慧星光（系统账号）推送",
    //         "reportOrgAndTime": "智慧星光（2025-08-27 21:06）",
    //         "jcczStatus": null,
    //         "reportUnit": null,
    //         "sendMessage": 0,
    //         "modifyValues": null,
    //         "videoFile": null,
    //         "evidenceIds": null
    //     }
    // ]
    dataList.value = dataList.value.concat(res.data);
    if (dataList.value.length >= count.value) {
      finished.value = true;
    }
    loading.value = false;

    // 保存数据状态
    scrollPosition.saveScrollAndData({
      dataList: dataList.value,
      pageNo: pageNo.value,
      finished: finished.value,
      count: count.value,
      keyword: keyword.value
    });
  });
};
const handleSearch = () => {
  finished.value = false;
  pageNo.value = 0;
  dataList.value = [];
  getCount();
};
onMounted(() => {
  // 尝试恢复页面数据
  const savedData = scrollPosition.restorePageData();
  if (savedData) {
    // 恢复数据
    dataList.value = savedData.dataList || [];
    pageNo.value = savedData.pageNo || 0;
    finished.value = savedData.finished || false;
    count.value = savedData.count || 0;
    keyword.value = savedData.keyword || '';

    // 恢复滚动位置
    scrollPosition.restoreScrollPosition();
  } else {
    // 如果没有数据，正常加载
    getCount();
  }

  getLog("涉济舆情/首页", "浏览");

  nextTick(() => {
    document.title = "涉济舆情";
  });

  // 初始化滚动位置管理
  scrollPosition.onMounted();
});

// 页面卸载前保存数据
onBeforeUnmount(() => {
  scrollPosition.saveScrollAndData({
    dataList: dataList.value,
    pageNo: pageNo.value,
    finished: finished.value,
    count: count.value,
    keyword: keyword.value
  });
  scrollPosition.onBeforeUnmount();
});
</script>
<style lang="less" scoped>
/* @import url(); 引入css类 */
.PublicSentiment {
  height: calc(~"100vh");
  overflow-y: auto;
  .list {
    height: 100%;
  }
}
</style>
