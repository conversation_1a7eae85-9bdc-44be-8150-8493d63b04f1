<!-- 列表单项 -->
<template>
  <div class="item">
    <div
      @click="openURL(itemInfo)"
      :class="['title', itemInfo.situation === 10 ? 'ellipsis-3' : 'ellipsis']"
    >
      <SvgIcon
        :name="situationIcon[itemInfo.situation]"
        width="20"
        height="20"
      />
      {{ itemInfo.mtitle }}
    </div>
    <div v-if="itemInfo.situation !== 10" class="mabstract ellipsis-2">
      <span v-html="removeROrN(itemInfo.mabstract)"></span>
    </div>
    <div class="remark">
      <div class="date">
        {{ moment(itemInfo.mpublishTime).format("MM-DD HH:mm") }}
      </div>
      <div class="situation ellipsis">
        {{ itemInfo.mwebsiteName || situations[itemInfo.situation] }}
      </div>
      <div class="copy-link" @click="copyLink" v-if="itemInfo.murl">
        复制链接
      </div>
      <div class="tag">
        <div class="area ellipsis">
          {{ itemInfo.marea ? handleJSON(itemInfo.marea, "其他") : "其他" }}
        </div>
        <div class="type ellipsis">
          {{
            itemInfo.scopeArea
              ? handleJSON(itemInfo.scopeArea, "未分类")
              : "未分类"
          }}
        </div>
        <div
          :class="[
            'emotion',
            itemInfo.msentiment === 1
              ? 'negative'
              : itemInfo.msentiment === 0
              ? 'neutral'
              : 'front',
          ]"
        >
          {{
            itemInfo.msentiment === 1
              ? "正"
              : itemInfo.msentiment === 0
              ? "中"
              : "负"
          }}
        </div>
      </div>
    </div>
    <SvgIcon
      width="30"
      height="30"
      name="已删除1"
      class="tips"
      v-if="itemInfo.isMsgDel === 1"
    />
  </div>
</template>

<script setup lang="ts">
import { useDomainListStore } from "@/stores/domainList.js";
const domainListStore = useDomainListStore();
import { getLogDetial } from "@/services/api";
import { showToast } from 'vant';
import { useRouter } from 'vue-router';
import ThirdPartyAppLauncher from '@/utils/thirdPartyAppLauncher';

//生命周期 - 创建完成（访问当前this实例）
const { itemInfo } = defineProps(["itemInfo"]);
import { ref } from "vue";
import moment from "moment";

const router = useRouter();
const situations: any = {
  0: "全部",
  30: "媒体网站",
  31: "客户端",
  10: "新浪微博",
  199: "短视频",
  20: "微信公众号",
  80: "小红书",
  60: "论坛贴吧",
  600: "今日头条",
};
const situationIcon: any = {
  41: "论坛跟帖",
  51: "贴吧跟帖",
  48: "视频",
  50: "视频",
  110: "火山",
  188: "视频",
  199: "视频",
  30: "新闻",
  31: "新闻app",
  10: "微博",
  20: "微信公号",
  80: "小红书",
  60: "论坛",
  61: "论坛",
  62: "贴吧",
  170: "知乎",
  230: "自媒体",
  300: "视频300",
  200: "视频",
  320: "新闻网站",
  400: "境外新闻网站",
  501: "Facebook",
  502: "推特",
  600: "今日头条",
};
const removeROrN = (str: string) => {
  if (str) {
    return str
      .replace(/(\r\n|\n|\r|\\r\\n|\\r|\\n)/g, "")
      .replace(/<[^>]+>/g, "")
      .trim();
  } else {
    return "";
  }
};
// 解析URL获取笔记ID
  function parseNoteId(url) {
    try {
      // 匹配URL中的笔记ID，格式为/item/[0-9a-z]+
      const regex = /\/item\/([0-9a-z]+)/i;
      const match = url.match(regex);
      if (match && match[1]) {
        return match[1];
      }
      
      return '';
    } catch (error) {
      console.error('解析笔记ID出错', error);
      
      return '';
    }
  }
  // 生成DeepLink
    function generateDeepLink(noteId) {
     
      // 构建基础DeepLink格式
      let link = `xhsdiscover://item/discovery.${noteId}`;
      
      return link;
    }
    
const openURL = async (obj: any) => {
  getLogDetial("涉济舆情/详情页","浏览",obj?.mtitle,obj?.mkey);

  // 保存当前滚动位置到localStorage（外链跳转用）
  const container = document.querySelector('.PublicSentiment');
  if (container) {
    const scrollTop = container.scrollTop;
    localStorage.setItem('publicSentiment_scroll', scrollTop.toString());
    console.log('点击详情前保存滚动位置:', scrollTop);
  }
  var noteId = parseNoteId(itemInfo.murl);
    
  // 检查是否为小红书链接，如果是则尝试唤起小红书应用
  if (ThirdPartyAppLauncher.isXiaohongshuUrl(itemInfo.murl) && noteId) {
    console.log('检测到小红书链接，尝试唤起小红书应用');
    const success = await ThirdPartyAppLauncher.launchXiaohongshu(generateDeepLink(noteId));

    if (!success) {
      console.log('唤起小红书应用失败，已自动使用浏览器打开');
    }
  } else {
    // 非小红书链接，保持原有逻辑
    window.location.href = itemInfo.murl;
  }
};

// 复制链接功能
const copyLink = async () => {
  try {
    if (itemInfo.murl) {
      // 使用现代浏览器的 Clipboard API
      if (navigator.clipboard && window.isSecureContext) {
        await navigator.clipboard.writeText(itemInfo.murl);
      } else {
        // 降级方案：使用传统的 document.execCommand
        const textArea = document.createElement('textarea');
        textArea.value = itemInfo.murl;
        textArea.style.position = 'fixed';
        textArea.style.left = '-999999px';
        textArea.style.top = '-999999px';
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
      }

      // 显示成功提示
      showToast({
        message: '链接已复制到剪贴板',
        type: 'success'
      });

      // 记录日志
      getLogDetial("涉济舆情/详情页","复制链接",itemInfo?.mtitle,itemInfo?.mkey);
    }
  } catch (err) {
    console.error('复制失败:', err);
    showToast({
      message: '复制失败，请手动复制',
      type: 'fail'
    });
  }
};
const handleJSON = (data: string, str: string) => {
  if (data) {
    // let resultString = data.replace(/[\[\]"]/g, "");
    let resultString = data.replace(/[\[\]"]/g, "");
    if (resultString.indexOf("涉济,") > -1) {
      resultString = resultString.replace(/涉济,/g, "");
    }
    if (resultString.indexOf(",涉济") > -1) {
      resultString = resultString.replace(/,涉济/g, "");
    }
    if (!resultString) {
      return str;
    }
    // 使用正则表达式替换逗号周围的空白字符
    //resultString = resultString.replace(/\s*,\s*/g, ",");
    if (resultString != "其他" && resultString != "未分类") {
      let list = [];
      let newList: string[] = [];
      if (resultString.indexOf(",") == -1) {
        list.push(resultString);
      } else {
        list = resultString.split(",");
      }
      domainListStore.list &&
        domainListStore.list.forEach((v: any) => {
          if (list.includes(v.name) && v.children.length == 0) {
            newList.push(v.name);
          }
          if (v.children && v.children.length > 0) {
            v.children.forEach((k: any) => {
              if (list.includes(k.name)) {
                newList.push(v.name + "-" + k.name);
              }
            });
          }
        });
      if (newList.length > 0) {
        return newList.join(",");
      } else {
        return resultString;
      }
    } else {
      return resultString;
    }
  } else {
    return str;
  }
};
</script>
<style lang="less" scoped>
/* @import url(); 引入css类 */
.item {
  padding: 10px;
  border-bottom: 3px solid #f2f2f2;
  line-height: 20px;
  position: relative;
  .title {
    font-weight: 600;
  }
  .mabstract {
    line-height: 20px;
    height: 40px;
  }
  .remark {
    display: flex;
    justify-content: space-between;
    margin-top: 10px;
    color: #aaaaaa;
    font-size: 12px;
    .date {
    white-space: nowrap;
    }
    .copy-link {
      padding: 0px 3px;
      background-color: #f0f8ff;
      color: #5585ec;
      border: 1px solid #5585ec;
      border-radius: 2px;
      font-size: 10px;
      cursor: pointer;
      white-space: nowrap;
      margin: 0 5px;
      transition: all 0.2s ease;

      &:hover {
        background-color: #5585ec;
        color: #fff;
      }

      &:active {
        transform: scale(0.95);
      }
    }
    .situation {
      padding: 0 5px;
      
    }
    .tag {
      flex: 1;
      display: flex;
      justify-content: right;

      .area {
        height: 20px;
        border: 0.5px solid #5585ec;
        padding: 0 5px;
        font-size: 10px;
        max-width: 45px;
      }
      .type {
        background-color: #dee7fc;
        padding: 0 5px;
        margin: 0 5px;
        font-size: 10px;
      }
      .emotion {
        padding: 0 5px;
        color: #fff;
        width: 12px;
      }

      .negative {
        background: #ffbc00;
      }

      .front {
        background: #e93d61;
      }

      .neutral {
        background: #5585ec;
      }
    }
  }
  .tips {
    position: absolute;
    top: 0;
    right: 0;
  }
}
</style>
