<template>
  <div>
    <van-tabs
      v-model:active="active"
      @click-tab="onClickTab"
      background="#f4f4f4"
    >
      <van-tab v-for="itm in navLists" :title="itm"> </van-tab>
      <RouterView />
    </van-tabs>
  </div>
</template>

<script lang="ts" setup>
import { RouterView, useRoute, useRouter } from "vue-router";
import { onMounted, reactive, ref } from "vue";
import { getLog } from "@/services/api";
const router = useRouter();
const route = useRoute();
const active = ref(0);
let navLists = reactive<any[]>(["待办提示单", "近三个月提示单"]);

const onClickTab = ({ title }: { title: any }) => {
  let path = "";
  if (title == "待办提示单") {
    path = "/PromptSheet/TodoList";
  } else {
    path = "/PromptSheet/NearlyWeek";
    getLog("舆情提示", "近三个月提示单浏览");
  }

  router.push({
    path: path,
  });
};
onMounted(() => {
  if (route.path.indexOf("/PromptSheet/NearlyWeek") > -1) {
    active.value = 1;
  }
  getLog("舆情提示", "浏览");
});
</script>

<style lang="less" scoped>
:deep(.van-tab--active) {
  color: #0e70fd;
}
</style>
