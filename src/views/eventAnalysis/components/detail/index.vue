<template>
  <div class="navs">
    <div class="title bk">{{ eventItm.eventName }}</div>
    <OuterFrame name="事件分析" />

    <div style="padding: 12px 10px;" class="bk">
      {{ eventItm.eventDescription ? eventItm.eventDescription : "无摘要" }}
    </div>

    <EventDescription :eventInfo="eventItm" />
    <OuterFrame name="首发情况" />
    <InitialSituation :eventInfo="eventItm" />
    <OuterFrame name="数据概览" />
    <DataOverview
      :eventInfo="eventItm"
      :maxSiution="maxSiution"
      :dataDesc="dataDesc"
      :maxNum="maxNum"
      :siutionNum="siutionNum"
      :dataArr="dataArr"
      :endArr="endArr"
      :key="componentKey"
    />

    <OuterFrame name="传播趋势" />
    <PropagationTend :eventInfo="eventItm" />
    <div>
      <OuterFrame name="最新信息" />
      <listItem v-for="item in dataDesc.newsList" :itemInfo="item" :key="item.mkey"></listItem>
    </div>
   
  </div>
</template>
<script lang="ts" setup>
import moment from "moment";
import { useRoute, useRouter } from "vue-router";
import { onMounted, reactive, ref, nextTick } from "vue";
import {
  getEventInfo,
  getEventDataDesc,
  getEventDataSituation,
  getLog,
} from "@/services/api";
import OuterFrame from "./components/outerFrame.vue";
import EventDescription from "./components/eventDescription.vue";
import InitialSituation from "./components/initialSituation.vue";
import DataOverview from "./components/dataOverview.vue";
import PropagationTend from "./components/propagationTend.vue";
import listItem from "./components/listItem.vue";
const componentKey = ref(0);
const router = useRouter();
const route = useRoute();
const eventItm = ref({
  eventName: "",
  eventDescription: "",
});
const maxSiution = ref("");
let dataDesc = ref<any>({
  siution: {},
  maxNum: [],
  newsList: [],
});
const maxNum = ref(0);
let siutionNum = reactive<any[]>([]);
let dataArr = reactive<any[]>([]);
let endArr = reactive<any[]>([]);

const getData = async () => {
  let params = {
    eventId: route.query.eventId,
  };
  let response = await getEventInfo(params);
  eventItm.value = response.data;
  getLog("舆情事件", "浏览/" + response.data.eventName);
};
const getDataDesc = async () => {
  let params = {
    eventId: route.query.eventId,
  };
  let response = await getEventDataDesc(params);
  dataDesc.value = response.data;
  siutionNum.length = 0;
  for (const key in dataDesc.value.siution) {
    let value: string =
      dataDesc.value.siution[key as keyof typeof dataDesc.value.siution];
    const arrt = value.split("_");
    const siution = arrt[0];
    const num = arrt[1];
    let mo = {
      siution: siution,
      num: num,
    };
    siutionNum.push(mo);
  }
  //最多数据量平台获取
  let map = dataDesc.value.maxNum;

  for (const key in map) {
    maxNum.value = map[key];
    maxSiution.value = key;
  }
};

const getDataArr = async () => {
  dataArr.length = 0;
  let params = {
    eventId: route.query.eventId,
  };
  let response = await getEventDataSituation(params);
  let data = response.data;
  let keyArr = Object.keys(data);
  let endList = reactive<any[]>([]);
  let newArr = reactive<any[]>([]);
  if (keyArr.length >= 4) {
    newArr = [...[keyArr[0], keyArr[1], keyArr[2], keyArr[keyArr.length - 1]]];
  } else {
    if (keyArr.length == 3) {
      newArr = [...[keyArr[0], keyArr[1], 2, keyArr[keyArr.length - 1]]];
    }
    if (keyArr.length == 2) {
      newArr = [...[keyArr[0], 1, 2, keyArr[keyArr.length - 1]]];
    }
  }
  newArr.forEach((i) => {
    dataArr.push({
      10: data[i] ? data[i][0] : "-",
      20: data[i] ? data[i][1] : "-",
      30: data[i] ? data[i][2] : "-",
      31: data[i] ? data[i][3] : "-",
      60: data[i] ? data[i][4] : "-",
      600: data[i] ? data[i][5] : "-",
      80: data[i] ? data[i][6] : "-",
      199: data[i] ? data[i][7] : "-",
      // 1000: data[i][data[i].length - 1],
    });
    endList.push({ 1000: data[i] ? data[i][data[i].length - 1] : "-" });
  });
  endArr = [...endList];
  componentKey.value += 1;
};

onMounted(() => {
  getData();
  getDataDesc();
  // getDataArr();
  nextTick(() => {
    document.title = "事件分析";
    getDataArr();
  });
});
</script>
<style lang="less" scoped>
.navs {
  height: calc(~"100vh - 50px");
  overflow-y: auto;
}
.title {
  font-weight: 700;
  padding: 16px 10px;
  margin: 10px 0px;
}

.bk {
  background: #fff;
  font-family: HarmonyOS Sans;
  color: #333333;
  font-size: 14px;
  overflow-wrap: anywhere;
}
</style>
