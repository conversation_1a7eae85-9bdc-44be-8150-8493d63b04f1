<template>
     <div class="nav">
                <span class="xian"></span> {{name}}
            </div>
</template>
<script lang="ts" setup>
const { name } = defineProps(["name"]);

</script>


<style lang="less" scoped>
.nav {
    display: flex;
    align-items: center;
    height: 41px;
    border-bottom: 1px solid #c9c9c9;
    padding-left: 12px;
    background: #fff;
    .xian {
      width: 3px;
      height: 14px;
      background: #3e6efe;
      margin-right: 3px;
    }
}

</style>