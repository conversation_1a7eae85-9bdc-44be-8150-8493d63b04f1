<template>
  <div style="margin-bottom: 10px;">
    <div class="ct">
      <div class="itm" style="border-right: 1px solid #c9c9c9">
        <span>
          <SvgIcon name="监测时间" width="20px" height="20px" style="margin-right:3px;"/> 监测时间：
        </span>
        <span class="itm-r ">{{
          moment(eventInfo.monitorStartTime).format("YYYY-MM-DD HH:mm") +
          "-至今"
        }}</span>
      </div>
      <div class="itm">
        <span>
          <SvgIcon name="涉及地域" width="20px" height="20px" style="margin-right:3px;"/>  涉及地域：
        </span>
        <span class="itm-r">{{ eventInfo.areas ? eventInfo.areas:'-' }}</span>
      </div>
    </div>
    <div class="ct">
      <div class="itm" style="border-right: 1px solid #c9c9c9">
        <span>
          <SvgIcon name="参与网站" width="20px" height="20px" style="margin-right:3px;"/> 参与网站数：
        </span>
        <span class="itm-r">{{eventInfo.siteCnt ? eventInfo.siteCnt :'-'}}</span>
      </div>
      <div class="itm">
        <span>
          <SvgIcon name="涉及机构" width="20px" height="20px" style="margin-right:3px;"/>  涉及机构：
        </span>
        <span class="itm-r">{{ eventInfo.organs ? eventInfo.organs :'-' }}</span>
      </div>
    </div>
    <div class="ct" style=" border-bottom: 1px solid #c9c9c9;">
      <div class="itm" style="border-right: 1px solid #c9c9c9">
        <span>
          <SvgIcon name="参与账号" width="20px" height="20px" style="margin-right:3px;"/> 参与账号数：
        </span>
        <span class="itm-r">{{eventInfo.accountCnt ? eventInfo.accountCnt :'-'}}</span>
      </div>
      <div class="itm">
        <span>
          <SvgIcon name="涉及人物" width="20px" height="20px" style="margin-right:3px;"/>  涉及人物：
        </span>
        <span class="itm-r">{{ eventInfo.persons ? eventInfo.persons :'-' }}</span>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import moment from "moment";
const { eventInfo } = defineProps(["eventInfo"]);
</script>


<style lang="less" scoped>
.ct {
  display: flex;
}
.itm {
    display: flex;
    flex-direction: column;
    width: 50%;
    border-top: 1px solid #c9c9c9;
    background: #fff;
    padding: 8px 16px 10px 16px;
    overflow: hidden;
    span {
        display: flex;
        align-items: center;
    }
    .itm-r {
        color: #7c7c7c;
        font-family:PingFang SC;
        font-size: 14px;
        height: 44px;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        overflow: hidden;
        -webkit-line-clamp: 2;
        /* 控制显示的行数 */
    }
}
</style>