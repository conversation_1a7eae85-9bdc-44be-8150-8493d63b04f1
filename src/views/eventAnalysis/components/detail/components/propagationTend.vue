<template>
  <div class="propage">
    <div class="bt">
      <div
        v-for="item in typeList"
        :key="item.id"
        :class="['item', type == item.id ? 'active' : '']"
        @click="changeType(item.id)"
      >
        {{ item.name }}
      </div>
    </div>
    <div id="main" style="width: 100%; height: 350px"></div>
  </div>
</template>
<script lang="ts" setup>
import moment from "moment";
import * as echarts from "echarts";
import { onMounted, reactive, ref } from "vue";
import { useRoute, useRouter } from "vue-router";
import { getEventSpreadTends } from "@/services/api";
const { eventInfo } = defineProps(["eventInfo"]);
const router = useRouter();
const route = useRoute();
let type = ref(2);
let resultData = reactive({
  total: [],
  xAxis: [],
  '总量':[],
  '正面':[],
  '负面':[],
});
const typeList = reactive<any[]>([
  {
    id: "1",
    name: "按天统计",
  },
  {
    id: "2",
    name: "按小时统计",
  },
]);

const changeType = (d:any) => {
  type.value = d;
  getData();
};

const getData = async () => {
  let params = {
    eventId: route.query.eventId,
    type: type.value,
  };
  let response = await getEventSpreadTends(params);
  let data = response.data;
  resultData = { ...data.dataY };
  const date1 = moment(data.dataX[0]);
  const date2 = moment(data.dataX[data.dataX.length - 1]);
  // 计算时间差（以分钟为单位）
  const diffInMinutes = date2.diff(date1, "minutes");
  // 判断是否小于 48 小时（2880 分钟）
  const format = type.value == 2 ? "yyyy-MM-DD HH:mm:ss" : "yyyy-MM-DD";
  resultData.xAxis = data.dataX.map((i:any) => moment(i).format(format));
  // setFloatingPoint(data.dataX, resultData[1000]);
  setCharts();
};
const setFloatingPoint = () => {};
const  handleData = (data:any) => {
     if (data) {
       return data.map((time:any, index:any) => [resultData.xAxis[index], time]);
     }
    };

const setCharts = () => {
  let chartDom = document.getElementById("main");
  let myChart = echarts.init(chartDom);
  myChart.clear();
  let option = {
    title: {
      text: "",
    },
    color: ['#5872c9', '#07c160', '#de3c36'],
    tooltip: {
      // trigger: "axis",
       trigger: "axis",
          confine: true,
          axisPointer: {
            show: false,
          },
    },
    legend: {
      icon:'roundRect',
      data: [
        "总量",
        "正面",
        "负面",
      ],
       selected: {
              "总量":true,
              "正面": false,
              "负面": false,
            },
    },
    grid: {
      left: "3%",
      right: "4%",
      bottom: "3%",
      containLabel: true,
    },
    toolbox: {
      feature: {
      },
    },
    xAxis: {
      type: "category",
      boundaryGap: false,
       splitLine: {
            show: false,
          },
      // data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
      data: resultData.xAxis,
    },
    yAxis: {
      type: "value",
    },
    series: [
      {
        name: "总量",
        type: "line",
        stack: "Total",
        areaStyle: {},
         smooth: true,
          symbolSize: 1,
          lineStyle: {
            width: 1,
          },
        data: handleData(resultData['总量']),
      },
      {
        name: "正面",
        type: "line",
        areaStyle: {},
        smooth: true,
          symbolSize: 1,
          lineStyle: {
            width: 1,
          },
        data: handleData(resultData['正面']),
      },
      {
        name: "负面",
        type: "line",
        areaStyle: {},
        smooth: true,
          symbolSize: 1,
          lineStyle: {
            width: 1,
          },
        data: handleData(resultData['负面']),
      },
    ],
  };
  option && myChart.setOption(option);
   setTimeout(() => {
        myChart.resize();
      }, 0);
};

onMounted(() => {
  getData();
});
</script>


<style lang="less" scoped>
.propage {
  position: relative;
}
.bt {
  border: 1px solid #6da0ee;
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
  display: flex;
  width: 175px;
  position: absolute;
  right: 0px;
  top: -35px;
  .item {
    padding: 0 10px;
    color: #6da0ee;
  }
  .active {
    background: #6da0ee;
    color: #ffffff;
  }
}
</style>