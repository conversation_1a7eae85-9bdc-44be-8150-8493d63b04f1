<!-- 列表单项 -->
<template>
  <div class="item">
    <div @click="openURL" :class="['title', 'ellipsis-2']">
      <SvgIcon
        :name="situationIcon[itemInfo.situation]"
        width="20"
        height="20"
        style="vertical-align: middle;"
      />
      {{ itemInfo.mtitle }}
    </div>
    <div class="remark">
      <div class="date" style="font-size: 3vw;">
        {{ moment(itemInfo.mpublishTime).format("YYYY-MM-DD HH:mm") }}
      </div>
      <div class="situation ellipsis" style="font-size: 3vw;">
        {{
          (situations[itemInfo.situation]
            ? situations[itemInfo.situation]
            : "") +
          (itemInfo.mwebsiteName ? "-" + itemInfo.mwebsiteName : "") +
          (itemInfo.uname ? "-" + itemInfo.uname : "")
        }}
      </div>
      <span
        :title="getMsgChannelId(itemInfo.msgChannelId)"
        style="font-size: 3vw;"
      >
        报送：{{ getMsgChannelId(itemInfo.msgChannelId) }}
      </span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useDomainListStore } from "@/stores/domainList.js";
const domainListStore = useDomainListStore();
import { getLog } from "@/services/api";

//生命周期 - 创建完成（访问当前this实例）
const { itemInfo } = defineProps(["itemInfo"]);
import { ref } from "vue";
import moment from "moment";
const situations: any = {
  0: "全部",
  30: "媒体网站",
  31: "客户端",
  10: "新浪微博",
  199: "短视频",
  20: "微信公众号",
  80: "小红书",
  60: "论坛贴吧",
  600: "今日头条",
};
const situationIcon: any = {
  41: "论坛跟帖",
  51: "贴吧跟帖",
  48: "视频",
  50: "视频",
  110: "火山",
  188: "视频",
  199: "视频",
  30: "新闻",
  31: "新闻app",
  10: "微博",
  20: "微信公号",
  80: "小红书",
  60: "论坛",
  61: "论坛",
  62: "贴吧",
  170: "知乎",
  230: "自媒体",
  300: "视频300",
  200: "视频",
  320: "新闻网站",
  400: "境外新闻网站",
  501: "Facebook",
  502: "推特",
  600: "今日头条",
};

const getMsgChannelId = (msgChannelId: string) => {
  const msgChannelIdMap = {
    trs: "拓尔思",
    zhxgt: "智慧星光",
    zhxgb: "智慧星光",
    dzwt: "大众网",
    dzwb: "大众网",
    swt: "舜网",
    swb: "舜网",
    midb: "蜜度",
    wxb: "网宣",
  };

  return msgChannelId
    ? msgChannelIdMap[
        Object.keys(msgChannelIdMap).find((key) =>
          msgChannelId.startsWith(key)
        ) as keyof typeof msgChannelIdMap
      ] || ""
    : "";
};
const removeROrN = (str: string) => {
  if (str) {
    return str
      .replace(/(\r\n|\n|\r|\\r\\n|\\r|\\n)/g, "")
      .replace(/<[^>]+>/g, "")
      .trim();
  } else {
    return "";
  }
};
const openURL = () => {
  getLog("舆情事件/事件详情-最新信息详情", "浏览");

  window.open(itemInfo.murl, "_blank");
};
const handleJSON = (data: string, str: string) => {
  if (data) {
    // let resultString = data.replace(/[\[\]"]/g, "");
    let resultString = data.replace(/[\[\]"]/g, "");
    if (resultString.indexOf("涉济,") > -1) {
      resultString = resultString.replace(/涉济,/g, "");
    }
    if (resultString.indexOf(",涉济") > -1) {
      resultString = resultString.replace(/,涉济/g, "");
    }
    if (!resultString) {
      return str;
    }
    // 使用正则表达式替换逗号周围的空白字符
    //resultString = resultString.replace(/\s*,\s*/g, ",");
    if (resultString != "其他" && resultString != "未分类") {
      let list = [];
      let newList: string[] = [];
      if (resultString.indexOf(",") == -1) {
        list.push(resultString);
      } else {
        list = resultString.split(",");
      }
      domainListStore.list &&
        domainListStore.list.forEach((v: any) => {
          if (list.includes(v.name) && v.children.length == 0) {
            newList.push(v.name);
          }
          if (v.children && v.children.length > 0) {
            v.children.forEach((k: any) => {
              if (list.includes(k.name)) {
                newList.push(v.name + "-" + k.name);
              }
            });
          }
        });
      if (newList.length > 0) {
        return newList.join(",");
      } else {
        return resultString;
      }
    } else {
      return resultString;
    }
  } else {
    return str;
  }
};
</script>
<style lang="less" scoped>
/* @import url(); 引入css类 */
.item {
  padding: 10px;
  border-bottom: 3px solid #f2f2f2;
  line-height: 20px;
  position: relative;
  background-color: #fff;
  .title {
    font-weight: 600;
  }
  .mabstract {
    line-height: 20px;
    height: 40px;
  }
  .remark {
    display: flex;
    // justify-content: space-between;
    margin-top: 10px;
    color: #aaaaaa;
    font-size: 12px;
    .date {
    }
    .situation {
      padding: 0 5px;
    }
  }
  .tips {
    position: absolute;
    top: 0;
    right: 0;
  }
}
</style>
