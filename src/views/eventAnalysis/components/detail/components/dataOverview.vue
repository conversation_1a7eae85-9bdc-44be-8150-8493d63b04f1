<template>
  <div class="contents">
    <div class="sbwy">
      监测显示，{{
        moment(eventInfo.monitorStartTime).format("MM月DD日 HH:mm:ss")
      }}至{{
        moment(eventInfo.monitorStopTime).isAfter(moment())
          ? "今"
          : moment(eventInfo.monitorStopTime).format("MM月DD日 HH:mm:ss")
      }}，网上涉“{{ eventInfo.eventName }}”相关信息共计<span class="col">{{
        dataDesc.total
      }}</span
      >条。其中媒体信息<span class="col">{{ dataDesc.wxCount || 0 }}</span
      >条，自媒体信息<span class="col">{{ dataDesc.total - (dataDesc.wxCount || 0 )}}</span
      >条。 从信息分布情况看，{{
        getSituation(maxSiution)
      }}是主要传播平台共<span class="col">{{ maxNum }}</span
      >条， 其次是<span v-for="(stat, statIndex) in siutionNum" :key="stat.siution">{{ getSituation(stat.siution)
        }}<span class="col">{{ stat.num }}</span>条{{ statIndex == siutionNum.length - 1 ? '。' : '、' }}
      </span>
      该事件负面信息占比为<span class="col"
        >{{ Number(dataDesc.nePer).toFixed(2) }}%</span
      >，正面信息占比为<span class="col"
        >{{ Number(dataDesc.poPer + dataDesc.neutPer).toFixed(2) }}%</span
      >。
    </div>

    <div class="ct-zong">
      <div class="nav">
        <div class="list"></div>
        <div v-for="itd in dayList" :key="itd" class="list bt">
          {{ itd }}
        </div>
      </div>
      <div class="ct">
        <div style="display: flex; width: 100%;">
          <div v-for="itc in situations" :key="itc" class="list bl px58">
            {{ itc }}
          </div>
        </div>

        <div style="width: 100%;">
          <div v-for="(itv, index) in dataArr" class="item-zong">
            <div v-for="(itc, index) in itv" class="list bl bt px58">
              {{ itc }}
            </div>
          </div>
        </div>
      </div>
      <div class="nav">
        <div class="list" style="border-top: 1px solid #989898;">总量</div>
        <div v-for="itd in endArr" :key="itd" class="list bt">
          {{ itd[1000] }}
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import moment from "moment";
const {
  eventInfo,
  maxSiution,
  dataDesc,
  maxNum,
  siutionNum,
  dataArr,
  endArr,
} = defineProps([
  "eventInfo",
  "maxSiution",
  "dataDesc",
  "maxNum",
  "siutionNum",
  "dataArr",
  "endArr",
]);
import { onMounted, reactive, ref, watch } from "vue";
const situations = reactive({
  10: "新浪微博",
  20: "微信公众号",
  30: "媒体网站",
  31: "客户端",
  60: "论坛贴吧",
  600: "今日头条",
  80: "小红书",
  199: "短视频",
});
let dayList = reactive<any[]>(["今日", "昨日", "前日", "总量"]);

const getSituation = (d: any) => {
  return situations[d as keyof typeof situations];
};
</script>

<style lang="less" scoped>
.col {
  color: #5585ec;
}
.contents {
  padding: 10px 5px 18px 13px;
  background: #fff;
}
.sbwy {
  word-break: break-all;
}
.ct-zong {
  position: relative;
  display: flex;
  width: 100%;
  margin-top: 10px;
  .ct {
    width: calc(~"100% - 46px");
    overflow-x: auto;
  }
  .list {
    width: 46px;
    height: 40px;
    line-height: 40px;
    text-align: center;
    background: #eff3ff;
    border: 1px solid #989898;
    white-space: nowrap;
    font-size: 12px;
  }
}
.item-zong {
  display: flex;
  width: 100%;
}
.px58 {
  width: 58px !important;
  flex-shrink: 0;
}
.bt {
  border-top: 0px !important;
}
.bl {
  border-left: 0px !important;
}
.br {
  border-right: 0px !important;
}
</style>
