<template>
  <div style="background: #fff; padding: 10px 6px 0px 17px">
    <div class="itm">
      <span class="itm-l">
        <SvgIcon
          name="首发舆论场"
          width="20px"
          height="20px"
          style="margin-right: 3px"
        />
        首发舆论场：
      </span>
      <span>{{ getSituation(eventInfo.firstSituation) ? getSituation(eventInfo.firstSituation):'-' }}</span>
    </div>

    <div class="itm">
      <span class="itm-l">
        <SvgIcon
          name="首发网站"
          width="20px"
          height="20px"
          style="margin-right: 3px"
        />
        首发网站\账号：
      </span>
      <span>{{ eventInfo.firstAccount ? eventInfo.firstAccount :'-' }}</span>
    </div>

    <div class="itm">
      <span class="itm-l">
        <SvgIcon
          name="首发时间"
          width="20px"
          height="20px"
          style="margin-right: 3px"
        />
        首发时间：
      </span>
      <span>{{
       eventInfo.firstTime ? moment(eventInfo.firstTime).format("YYYY-MM-DD HH:mm:ss")
      :'-'}}</span>
    </div>

    <div style="margin-bottom: 20px">
      <SvgIcon
        name="首发文章"
        width="20px"
        height="20px"
        style="margin-right: 3px"
      />
      首发文章：{{ eventInfo.firstTitle ? eventInfo.firstTitle : "-" }}
    </div>

    <div style="margin-bottom: 20px">
      <SvgIcon
        name="链接"
        width="20px"
        height="20px"
        style="margin-right: 3px"
      />
      链接：<span class="bc" @click="openURL(eventInfo.firstUrl)" :style="eventInfo.firstUrl ? '':'color:#333333;'">{{
        eventInfo.firstUrl ? eventInfo.firstUrl : "-"
      }}</span>
    </div>
    <!-- v-if="eventInfo.mediaTitle" -->
    <div class="videos" v-if="eventInfo.mediaTitle">
      <div style="margin-bottom: 20px">
        <SvgIcon
          name="媒体介入"
          width="20px"
          height="20px"
          style="margin-right: 3px"
        />
        媒体介入：{{ eventInfo.mediaTitle }}
      </div>

      <div style="margin-bottom: 20px">
        <SvgIcon
          name="链接"
          width="20px"
          height="20px"
          style="margin-right: 3px"
        />
        链接：<span class="bc" @click="openURL(eventInfo.mediaUrl)" :style="eventInfo.mediaUrl ? '':'color:#333333;'">{{
          eventInfo.mediaUrl ? eventInfo.mediaUrl : "-"
        }}</span>
      </div>

      <div style="margin-bottom: 20px">
        <SvgIcon
          name="首发时间"
          width="20px"
          height="20px"
          style="margin-right: 3px"
        />
        时间：{{ moment(eventInfo.mediaTime).format("YYYY-MM-DD HH:mm:ss") }}
      </div>
    </div>

     <div class="videos" v-if="eventInfo.soundTitle">
      <div style="margin-bottom: 20px">
        <SvgIcon
          name="首发文章"
          width="20px"
          height="20px"
          style="margin-right: 3px"
        />
        发声情况：{{ eventInfo.soundTitle }}
      </div>

      <div style="margin-bottom: 20px">
        <SvgIcon
          name="链接"
          width="20px"
          height="20px"
          style="margin-right: 3px"
        />
        链接：<span class="bc" @click="openURL(eventInfo.soundUrl)" :style="eventInfo.soundUrl ? '':'color:#333333;'">{{
          eventInfo.soundUrl ? eventInfo.soundUrl : "-"
        }}
        
        </span>
      </div>

      <div style="margin-bottom: 20px">
        <SvgIcon
          name="首发时间"
          width="20px"
          height="20px"
          style="margin-right: 3px"
        />
        时间：{{ moment(eventInfo.soundTime).format("YYYY-MM-DD HH:mm:ss") }}
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import moment from "moment";
const { eventInfo } = defineProps(["eventInfo"]);
import { onMounted, reactive, ref } from "vue";
const situations = reactive({
  0: "全部",
  30: "媒体网站",
  31: "客户端",
  10: "新浪微博",
  199: "短视频",
  20: "微信公众号",
  80: "小红书",
  60: "论坛贴吧",
  600: "今日头条"
});

const getSituation = ((d:any) => {
     return  situations [d as keyof typeof situations];
});
const openURL = (url:string) => {
  window.open(url, "_blank");
};
</script>


<style lang="less" scoped>
.itm {
  display: flex;
  font-family: HarmonyOS Sans;
  color: #333333;
  font-size: 14px;
  align-items: center;
  margin-bottom: 20px;
}
.itm-l {
  display: flex;
  align-items: center;
}
.itm-r {
  width: calc(~"100% - 100px");
}
.bc {
  color: #4485fd;
  overflow-wrap: anywhere;
}
.videos {
  padding: 10px 6px 0px 17px;
  margin: 0px -6px 0px -17px;
  border-top: 1px solid #c9c9c9;
}
</style>