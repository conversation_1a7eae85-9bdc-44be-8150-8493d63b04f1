<template>
    <div class="eventConent">
        <div class="title" @click="toDetails">
            <span>{{itemInfo.eventName}}</span>
            <span class="title-btn" v-if="name == '区县事件' && organType != 2">{{itemInfo.createOrganName}}</span>
        </div>
        <div class="btn">
            <span>信息量：{{itemInfo.total}}</span>
            <span>监测时间：{{ moment(itemInfo.monitorStartTime).format("YYYY-MM-DD HH:mm") + '-' +  moment(itemInfo.monitorStopTime).format("MM-DD HH:mm")}}</span>
        </div>
    </div>
</template>

<script setup lang="ts">
import moment from "moment";
const { itemInfo,index,name,organType} = defineProps(["itemInfo","index","name","organType"]);
import { useRoute, useRouter } from "vue-router";
const router = useRouter();
const route = useRoute();
import { getLog } from "@/services/api";

const toDetails = () => {
  getLog("舆情事件/事件详情", "浏览");
  router.push({
    path: "/eventAnalysis/Details",
    query: {
      eventId: itemInfo.eventId,
    },
  });
};
</script>
<style lang="less" scoped>
.eventConent {
    background: #fff;
    padding: 6px 12px;
    margin-top: 10px;
    .title {
        font-family:HarmonyOS Sans;
        color:#333333;
        font-size:14px;
        min-height: 40px;
        display: flex;
        justify-content: space-between;
        .title-btn {
            padding: 0px 3px ;
            height: 20px;
            line-height: 20px;
            border: 1px solid #6491EE;
            background: #E6F7FF;
            border-radius: 2px;
            margin-left: 5px;
        }
    }
    .btn {
        display: flex;
        justify-content: space-between;
        span {
            font-family:PingFang SC;
            color:#666666;
            font-size:12px;
        }
    }
}

</style>