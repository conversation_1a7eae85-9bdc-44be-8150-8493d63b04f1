<template>
  <div class="hotList">
    <van-tabs
      v-model:active="activeName"
      @click-tab="onClickTab"
      background="#f4f4f4"
    >
      <van-tab v-for="itm in navList" :title="itm.name" :name="itm.name">
        <van-empty v-if="!loading && count === 0" description="暂无数据" />
        <van-pull-refresh v-model="refreshing" @refresh="onRefresh" v-else>
          <van-list
            v-model:loading="loading"
            :finished="finished"
            finished-text="没有更多了"
            loading-text="加载中..."
            @load="getList"
          >
            <ListItem
              v-for="(item, index) in dataList"
              :itemInfo="item"
              :index="index"
              :name="activeName"
              :organType="organType"
              :key="item.id"
            />
          </van-list>
          <van-back-top bottom="10vh" right="5vw" />
        </van-pull-refresh>
      </van-tab>
    </van-tabs>
  </div>
</template>

<script lang="ts" setup>
import moment from "moment";
import { onMounted, reactive, ref, nextTick } from "vue";
import { eventListfindAll, eventListfindList, getLog } from "@/services/api";
import ListItem from "./components/listItm/index.vue";
import { useRoute, useRouter } from "vue-router";

const router = useRouter();
const route = useRoute();
const count = ref(0);
const loading = ref(false);
const finished = ref(false);
const refreshing = ref(false);
const pageNo = ref(0);
let activeName = ref("市办事件");
let organType = ref();
let navList = reactive<any[]>([]);
let dataList = reactive<any[]>([]);
const getData = async () => {
  
  dataList = [];
  dataList.length = 0;
  count.value = 0;
  loading.value = true;
  setTimeout(() => {
    if (refreshing.value) {
      dataList.length = 0;
      refreshing.value = false;
    }
  });
  let organType = localStorage.getItem("organType");
  let response;
  let params: any = {
    keyword: "",
    status: 1,
    type: 0,
    pageNo: 1,
    pageSize: 50,
  };
  if (activeName.value == "市办推送") {
      params.isCounty = 2;
    }
    if (activeName.value == "区县事件") {
      params.isCounty = 3;
  }
  params.organType = organType;
  params.organId = localStorage.getItem("organId");
  // params.organType = 2;
  // params.organId = 239;
  response = await eventListfindAll(params);
  count.value = response.data;
  if (count.value > 0) {
    getList();
  } else {
    loading.value = false;
  }
};

const getList = async () => {
  pageNo.value++;
  let organType = localStorage.getItem("organType");
  let response;
  let params: any = {
    keyword: "",
    status: 1,
    type: 0,
    pageNo: pageNo.value,
    pageSize: 50,
  };

  if (activeName.value == "市办推送") {
      params.isCounty = 2;
    }
    if (activeName.value == "区县事件") {
      params.isCounty = 3;
  }
  params.organType = organType;
  params.organId = localStorage.getItem("organId");
  // params.organId = 0;
  // params.isCounty = 1;
  // params.organType = 2;
  // params.organId = 239;
  response = await eventListfindList(params);
  dataList = dataList.concat(response.data ? response.data : []);
  loading.value = false;
  if (dataList.length > 0 && dataList.length >= count.value) {
    finished.value = true;
  }
};
const onRefresh = () => {
  pageNo.value = 0;
  finished.value = false;
  getData();
};

const onClickTab = ({ title }: { title: any }) => {
  getLog("舆情事件/" + title, "浏览");
  pageNo.value = 0;
  dataList.length = 0;
  finished.value = false;
  getData();
};

onMounted(() => {
  getData();
  // getLog("舆情事件", "浏览");
  nextTick(() => {
    document.title = "舆情事件";
    organType.value = localStorage.getItem("organType");
    if (organType.value == 2) {
      activeName.value = "区县事件";
      let quList = [
        { id: 1, name: "区县事件" },
        { id: 2, name: "市办推送" },
      ];
      navList = {
        ...navList,
        ...quList,
      };
    } else if (organType.value == 1) {
      navList = {
        ...navList,
        ...[
          { id: 1, name: "市办事件" },
          { id: 2, name: "区县事件" },
        ],
      };
    } else {
      activeName.value = "网信办推送事件";
      let quList = [{ id: 1, name: "网信办推送事件" }];
      navList = {
        ...navList,
        ...quList,
      };
    }
  });
});
</script>

<style lang="less" scoped>
:deep(.van-tab--active) {
  color: #0e70fd;
}

.hotList {
  height: calc(~"100vh - 50px");
  overflow-y: auto;
}
</style>
