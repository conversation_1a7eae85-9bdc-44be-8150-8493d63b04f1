<template>
  <div class="todoList">
    <!-- <HeaderSearch @search="search" /> -->
    <Legend :num="count" />
    <van-empty v-if="!loading && count === 0" description="暂无数据" />
     <van-pull-refresh v-model="refreshing" @refresh="onRefresh" v-else>
    <van-list
      v-model:loading="loading"
      :finished="finished"
      finished-text="没有更多了"
      loading-text="加载中..."
      @load="getData"
    >
      <ListItem v-for="item in dataList" :itemInfo="item" />
    </van-list>
    <van-back-top bottom="10vh" right="5vw" />
    </van-pull-refresh>
  </div>
</template>
<script lang="ts" setup>
import HeaderSearch from "@/components/headerSearch/index.vue";
import Legend from "./components/legend/index.vue";
import ListItem from "./components/listItem/index.vue";
import { onMounted, reactive, ref ,nextTick} from "vue";
import { findAllAPI, findAll,} from "@/services/api";
import { useRoute } from "vue-router";
const route = useRoute();
const count = ref(0);
const loading = ref(false);
const finished = ref(false);
const refreshing = ref(false);
const pageNo = ref(0);
const timeTypeNum = ref(localStorage.getItem('organId') && Number(localStorage.getItem('organId')) == 221 ? 7 : 0)
let dataList = reactive<any[]>([]);

const search = () => {
  loading.value = true;
  count.value = 0;
  dataList.length = 0;
  pageNo.value = 0;
  getData();
};

const getData = async () => {
  loading.value = true;
  pageNo.value++;
  setTimeout(() => {
    if (refreshing.value) {
      dataList.length = 0;
      refreshing.value = false;
    }
  });
  let organType = localStorage.getItem("organType");
  let response;
  if (organType == "1") {
    const params = {
      timeType: timeTypeNum.value,
      status: 4,
      keyWord: route.query.keyword,
      pageNum: pageNo.value,
      pageSize: 20,
      organId: localStorage.getItem("organId"),
      orderType: "desc",
      orderBy: "send_time",
    };
    response = await findAll(params);
  } else {
    const params = {
      timeType: timeTypeNum.value,
      msgStatus: "2,3,5",
      msgType: 1,
      keyWord: route.query.keyword,
      pageNum: pageNo.value,
      pageSize: 20,
      organId: localStorage.getItem("organId"),
      orderType: "desc",
      orderBy: "send_time",
      isFinish: 0
    };
    response = await findAllAPI(params);
  }

  let data = response.data;
  count.value = data.count;

  dataList.push(...data.data);
  loading.value = false;
  if (dataList.length >= count.value) {
    finished.value = true;
  }
};

const onRefresh = () => {
  pageNo.value = 0;
  finished.value = false;
  getData();
};

onMounted(() => {
  getData();
  
  nextTick(() => {
   document.title = '舆情提示单';
  })
});

</script>
<style lang="less" scoped>
.todoList {
  height: calc(~"100vh");
  overflow-y: auto;
}
</style>
