<template>
  <div class="legend flex">
    <div class="flex label">
      <SvgIcon name="bell" width="24px" height="24px" />
      <span>
        {{ isSwOrgan ? '近一周未完结提示单：' : '待处理：' }}<span class="num">{{ props.num }}</span
        >个
      </span>
    </div>
    <div class="species flex" v-if="!isSwOrgan">
      <template v-if="organType == '2'||organType == '3'">
        <template v-for="(item, index) in legend" :key="index">
          <div class="item flex" v-if="item.id != 4">
            <div class="dot" :style="{ backgroundColor: item.color }"></div>
            <div class="name">{{ item.name }}</div>
          </div>
        </template>
      </template>
      <template v-else>
        <div class="item flex">
          <div class="dot" style="background-color: #aaa"></div>
          <div class="name">已反馈</div>
        </div>
      </template>
    </div>
  </div>
</template>
<script setup>
import legend from "@/assets/json/legend.json";
import { onBeforeMount, ref } from "vue";
const props = defineProps({
  num: {
    type: Number,
    default: 0,
  },
});

let organType = ref("1");
organType.value = localStorage.getItem("organType");
const isSwOrgan = ref(localStorage.getItem('organId') && Number(localStorage.getItem('organId')) == 221 ? true : false)
onBeforeMount(() => {});
</script>

<style lang="less" scoped>
.legend {
  // margin-top: 30px;
  padding: 10px;
  background-color: #fff;
  align-items: center;
  justify-content: space-between;
  font-size: 12px;
  .label {
    align-items: center;
    .num {
      font-weight: 600;
    }
  }
  .species {
    .item {
      align-items: center;
    }
  }
}
</style>
