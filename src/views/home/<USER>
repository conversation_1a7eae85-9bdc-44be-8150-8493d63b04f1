<!--
 * @Author: yang<PERSON>shan <EMAIL>
 * @Date: 2025-03-21 10:18:31
 * @LastEditors: yangfushan <EMAIL>
 * @LastEditTime: 2025-04-21 10:10:18
 * @FilePath: \app_web\src\views\home\index.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <van-empty
    image="error"
    v-if="status"
    :description="errorMessage"
  />
</template>

<script setup>
import { getUserInfo, getQxauthCode } from "@/services/api";
import { onBeforeMount, ref } from "vue";
import { useRoute, useRouter } from "vue-router";
const route = useRoute();
const router = useRouter();
const status = ref(false);
const errorMessage = ref("当前用户暂无该系统权限，如需使用，请联系管理员。");
import { useDomainListStore } from "@/stores/domainList";
const DomainListStore = useDomainListStore();

const setUserInfo = async (code) => {
  showLoadingToast({
    message: "加载中...",
    forbidClick: true,
    loadingType: "spinner",
  });
  const response = await getUserInfo({
    code: code,
  });
  if (response.status === 0) {
    const data = response.data;
    Object.keys(data).forEach((i) => {
      localStorage.setItem(i, data[i]);
    });
    DomainListStore.setUserId(data.userId);
    DomainListStore.setUserAccount(data.userAccount);
    DomainListStore.setUserName(data.userName);
    DomainListStore.setIphone(data.iphone);
    DomainListStore.setToken(data.token);
    DomainListStore.setOrganId(data.organId)
    DomainListStore.setDepartmentId(data.departmentId)
    if(data.organId == 230 && data.departmentId != 230 ){
      getQxauthCode().then((res) => {
        if(res.status == 0){
          window.location.replace('/lxYqNginx/?code=' + res.data)
          // window.open( '/lxYqNginx/?code=' + res.data, "_blank");
        }
      });
    }
    if(data.organId == 232 && data.departmentId != 232 ){
      getQxauthCode().then((res) => {
        if(res.status == 0){
          // window.open('/tqYqNginx/?code=' + res.data, "_blank");
          window.location.replace('/tqYqNginx/?code=' + res.data)
        }
      });
    }
    router.replace({
      path: "/NetInfoDynamic/CityDynamic",
      // path: "/hotList"
    });
  } else {

    closeToast();

    // 根据不同的错误状态码设置不同的提示信息
    if (response.status == -2) {
      errorMessage.value = "您的系统账号因超过30天未登录已被停用，如有疑问请联系系统管理员";
    } else {
      errorMessage.value = "当前用户暂无该系统权限，如需使用，请联系管理员。";
    }

    setTimeout(() => {
      status.value = true;
    }, 300);
  }
};

onBeforeMount(() => {
  let code = route.query.code;
  if (code) {
    setUserInfo(code);
  } else {
    let data = {
      userId: 48,
      userName: "srp",
      userAccount: "srp",
      organId: 230,
      organName: "拓尔思",
      departmentId: 230,
      departmentName: "拓尔思",
      depCode: "#244#",
      iphone: "***********",
      token: "d4063bde0364c5b6ba760f41dd96cb1d",//本地监测系统 token
    };
    Object.keys(data).forEach((i) => {
      localStorage.setItem(i, data[i]);
    });
    DomainListStore.setUserId(data.userId);
    DomainListStore.setUserAccount(data.userAccount);
    DomainListStore.setUserName(data.userName);
    DomainListStore.setIphone(data.iphone);
    DomainListStore.setToken(data.token);
    DomainListStore.setOrganId(data.organId)
    DomainListStore.setDepartmentId(data.departmentId)
    router.replace({
      path: "/NetInfoDynamic/CityDynamic",
      // path: "/hotList"
    });
  }
});
</script>

<style lang="less" scoped>
:deep(.van-empty__description) {
  padding: 0 100px;
}
</style>
