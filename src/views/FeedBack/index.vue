<template>
    <div class="wrap">
        <div class="title">请选择功能</div>
        <div class="tabbox">
            <div v-for="itm in tabList" :key="itm.id" :class="[tabid === itm.id?'active':'','tab-item']" @click="tabClick(itm)">
                {{ itm.txt }}
            </div>
        </div>
        
        <div class="title" style="margin-top:10px">问题和意见<span style="color:red">*</span> </div>
        <van-field
            v-model="inputText"
            type="textarea"
            rows="4"
            :maxlength="200"
            show-word-limit
            placeholder="您在那个页面，遇到了什么问题，详细描述有助于帮您快速解决。"
        />
        <div class="title" style="margin-top:10px">上传图片</div>
        <div class="uploadwrap">
            <div class="custom-upload-btn" @click="triggerUpload">
                <van-icon name="plus" size="24" />
            </div>
            <van-uploader
                v-model="fileList"
                multiple
                :max-count="9"
                ref="uploaderRef"
                :show-upload="false"
                class="hidden-uploader"
                >
                <!-- <template #preview-cover="{ file }"> -->
                    <!-- <div class="preview-cover"> -->
                    <!-- <span v-if="file.status === 'uploading'">上传中...</span>
                    <span v-else-if="file.status === 'failed'">上传失败</span> -->
                    <!-- </div> -->
                <!-- </template> -->
            </van-uploader>
        </div>
        
        <div class="btn" @click="submit">提交</div>
    </div>
    
</template>

<script lang="ts" setup>
import { onMounted, reactive, ref, nextTick } from "vue";
import { postFeedback } from "@/services/api";
import type { UploaderInstance } from 'vant'; // 导入 Vant 的 Uploader 类型
import { showDialog } from 'vant';
import 'vant/es/dialog/style'; // 单独引入 Dialog 样式

let tabid = ref(1)
let inputText = ref('')
let tabList = ref<any[]>([]);
const uploaderRef = ref<UploaderInstance | null>(null);;
let fileList= ref<any[]>([]);


const updateTabList= ()=> {
  tabList.value = [
    {id: 1, value: '网信动态', txt: '网信动态'},
    {id: 2, value: '热搜榜单', txt: '热搜榜单'},
    {id: 3, value: '涉济舆情', txt: '涉济舆情'},
    {id: 4, value: '舆情提示单', txt: '舆情提示单'},
    {id: 5, value: '舆情事件', txt: '舆情事件'},
    {id: 6, value: '辟谣信息', txt: '辟谣信息'},
    {id: 7, value: '其他', txt: '其他'}
  ]
}

// 触发文件选择
const triggerUpload = () => {
  uploaderRef.value?.chooseFile();
};

// tab点击事件
const tabClick = (item:any)=> {
    tabid.value = item.id
}

// 提交
const submit = async ()=> {
    if(!inputText.value) {
        // return showToast('请输入问题和意见');
        showDialog({
            message:'请输入问题和意见',
        });
        return false
    }
    let params = new FormData();
    params.append("type", tabid.value.toString());
    params.append("suggest", inputText.value);
    
    fileList.value.forEach((file) => {
      params.append("files", file.file);
    });
    postFeedback(params).then(res=> {
        if(res.status === 0) {
            showDialog({
                message:'提交成功！',
            });
            tabid.value = 1
            inputText.value = ''
            fileList.value = []
        }
        
    }); 
}

onMounted(() => {
    updateTabList()
    
});
</script>

<style lang="less" scoped>
.wrap {
    padding: 10px 20px;
    margin-bottom: 30px;
    .uploadwrap {
        display: flex;
        .custom-upload-btn {
            margin-right: 10px;
            width: 80px;
            height: 80px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            background-color: #ffffff;
            border-radius: 4px;
            color: #969799;
            font-size: 12px;
            cursor: pointer;
        }
    }
    textarea {
        padding-top: 10px;
        width: 100%;
        border-radius: 8px;
        border: none;
    }
    .tabbox {
        margin-top: 15px;
        display: flex;
        justify-content: space-between;
        flex-wrap: wrap;
        gap: 10px;
        .tab-item {
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 15px;
            width: 100px;
            height: 30px;
            background: #fff;
            border: 1px solid #ccc;
        }
        .active {
            color: #1989FA;
            border: 1px solid #1989FA;
            background-color: rgb(230,247,255);
            
        }
        .normal {
            
        }
    }
    .title {
        color: #333;
        margin-bottom: 10px;
    }
    .btn {
        margin-top: 30px;
        background-color: rgb(116,149,206);
        display: flex;
        align-items: center;
        justify-content: center;
        color: #fff;
        width: 100%;
        height: 40px;
        border-radius: 20px;
    }
}
/deep/.van-uploader__upload {
    border-radius: 8px;
    background-color: #fff;
    
}
/deep/.van-popup {
    padding: 30px;
}


</style>