<template>
  <div class="hotList">
    <van-tabs
      v-model:active="activeName"
      @click-tab="onClickTab"
      background="#f4f4f4"
    >
      <van-tab v-for="itm in navList" :key="itm.id" :title="itm.name" :name="itm.name">
        <van-empty
          v-if="!loading && dataList.length === 0"
          description="暂无数据"
        />
        <van-pull-refresh v-model="refreshing" @refresh="onRefresh" v-else>
          <div v-if="dataList.length > 0" class="hotList-time">
            更新时间：{{
              moment(dataList[0].publishTime).format("YYYY-MM-DD HH:mm:ss")
            }}
          </div>
          <van-list
            v-model:loading="loading"
            :finished="finished"
            finished-text="没有更多了"
            loading-text="加载中..."
            @load="getList"
          >
            <ListItem
              v-for="(item, index) in dataList"
              :key="item.msgId || index"
              :itemInfo="item"
              :index="index"
              :activeTab="activeName"
            />
          </van-list>
          <van-back-top bottom="10vh" right="5vw" />
        </van-pull-refresh>
      </van-tab>
    </van-tabs>
  </div>
</template>

<script lang="ts" setup>
import moment from "moment";
import { onMounted, onBeforeUnmount, reactive, ref, nextTick } from "vue";
import { hotListfindList, getLog } from "@/services/api";
import ListItem from "./components/listItm/index.vue";
import { useScrollPosition } from "@/composables/useScrollPosition";
const activeName = ref("头条热榜");
const refreshing = ref(false);
const loading = ref(false);
const finished = ref(false);
const pageNo = ref(0);
let navList = reactive<any[]>([
  { id: 1, name: "头条热榜" },
  { id: 7, name: "头条同城榜" },
  { id: 6, name: "抖音热榜" },
  { id: 9, name: "抖音同城榜" },
  { id: 2, name: "微博热榜" },
  { id: 8, name: "微博同城榜" },
  { id: 3, name: "百度热榜" },
  { id: 4, name: "知乎热榜" },
]);
let dataList = reactive<any[]>([]);

// 使用滚动位置保持功能，支持tab切换，改为外链跳转模式
const scrollPosition = useScrollPosition({
  pageKey: 'hotList',
  containerSelector: '.hotList',
  supportTabs: true,
  activeTab: activeName.value,
  navigationMode: 'externalLink',
  debug: true
});
const getList = () => {
  loading.value = true;
  pageNo.value++;
  let list = navList.filter((v) => v.name == activeName.value);
  const params = {
    sources: list[0]?.id,
    statuses: "",
    keyword: "",
    pageNo: pageNo.value,
    pageSize: 50,
  };
  setTimeout(() => {
    if (refreshing.value) {
      dataList.length = 0;
      refreshing.value = false;
    }
  });
  // let response = await hotListfindList(params);
  hotListfindList(params).then((response) => {
    dataList = dataList.concat(response.data);
    if (response.data) {
      loading.value = false;
      if (response.data.length > 0) {
        finished.value = true;
      }
    } else {
    }

    // 保存数据状态
    scrollPosition.saveScrollAndData({
      dataList: dataList,
      pageNo: pageNo.value,
      finished: finished.value,
      activeTab: activeName.value
    });
  });
};
const onClickTab = ({ title }: { title: any }) => {
  console.log(title);

  // 更新activeTab并通知滚动位置管理器tab切换
  activeName.value = title;
  scrollPosition.updateActiveTab(title);
  scrollPosition.onTabChange(title);

  pageNo.value = 0;
  dataList.length = 0;
  finished.value = false;
  getLog("热搜榜单/" + title, "浏览");
  getList();
};
const onRefresh = () => {
  pageNo.value = 0;
  finished.value = false;
  getList();
};

onMounted(() => {
  // 首先尝试恢复页面数据，检查是否有任何tab的数据
  let savedData = scrollPosition.restorePageData();

  // 如果当前tab没有数据，尝试查找其他tab的数据
  if (!savedData) {
    // 遍历所有可能的tab，查找有数据的tab
    for (const navItem of navList) {
      // 临时更新activeTab来检查该tab是否有数据
      scrollPosition.updateActiveTab(navItem.name);
      const tabData = scrollPosition.restorePageData();
      if (tabData && tabData.dataList && tabData.dataList.length > 0) {
        savedData = tabData;
        activeName.value = navItem.name;
        console.log('找到保存的tab数据:', navItem.name);
        break;
      }
    }
  }

  if (savedData) {
    // 确保activeTab正确设置
    if (savedData.activeTab) {
      activeName.value = savedData.activeTab;
    }

    // 更新滚动位置管理器的activeTab
    scrollPosition.updateActiveTab(activeName.value);

    // 恢复数据
    dataList.length = 0;
    dataList.push(...(savedData.dataList || []));
    pageNo.value = savedData.pageNo || 0;
    finished.value = savedData.finished || false;

    // 恢复滚动位置
    scrollPosition.restoreScrollPosition();

    console.log('恢复到tab:', activeName.value, '数据条数:', dataList.length);
  } else {
    // 如果没有数据，使用默认tab并加载数据
    scrollPosition.updateActiveTab(activeName.value);
    getList();
  }

  getLog("热搜榜单/" + activeName.value, "浏览");
  nextTick(() => {
    document.title = "热搜榜单";
  });

  // 初始化滚动位置管理
  scrollPosition.onMounted();
});

// 页面卸载前保存数据
onBeforeUnmount(() => {
  scrollPosition.saveScrollAndData({
    dataList: dataList,
    pageNo: pageNo.value,
    finished: finished.value,
    activeTab: activeName.value
  });
  scrollPosition.onBeforeUnmount();
});
</script>

<style lang="less" scoped>
:deep(.van-tab--active) {
  color: #0e70fd;
}
.hotList {
  height: calc(~"100vh - 50px");
  overflow-y: auto;
}
.hotList-time {
  display: flex;
  min-height: 42px;
  align-items: center;
  border-bottom: 1px solid #d9d9d9;
  padding: 0px 12px;
  background: #fff;
  color: #8b99a2;
  font-size: 14px;
}
</style>
