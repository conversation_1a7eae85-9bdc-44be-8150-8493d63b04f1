<template>
  <div class="hot" @click="openURL">
    <div
      class="yuan"
      :style="
        index == 0
          ? 'background:linear-gradient(180deg,#e93d61 0%,#ff8b8b 100%);'
          : index == 1
          ? 'background:linear-gradient(180deg,#ff6c00 0%,#ffc98b 100%);'
          : index == 2
          ? 'background:linear-gradient(180deg,#ffbc00 0%,#ffe34a 100%);'
          : ''
      "
    >
      {{ index + 1 }}
    </div>
    <van-highlight
      :keywords="wordList"
      :source-string="itemInfo.keyword || ''"
      highlight-class="highlight0"
    />
  </div>
</template>

<script setup lang="ts">
//生命周期 - 创建完成（访问当前this实例）
import { reactive } from "vue";
const { itemInfo, index, activeTab } = defineProps(["itemInfo", "index", "activeTab"]);
import { getLog } from "@/services/api";
const wordList = reactive<any[]>([
  "济南",
  "历下区",
  "市中区",
  "槐荫区",
  "天桥区",
  "历城区",
  "长清区",
  "章丘区",
  "济阳区",
  "莱芜区",
  "钢城区",
  "平阴县",
  "商河县",
  "高新区",
  "南部山区",
  "起步区",
  "趵突泉",
  "黑虎泉",
  "大明湖",
  "泉城广场",
  "千佛山",
]);

const openURL = () => {
  getLog("热搜榜单/热榜详情", "浏览");

  // 使用传递的activeTab参数
  const currentTab = activeTab || '头条热榜';

  // 保存当前滚动位置到localStorage（外链跳转用）
  const container = document.querySelector('.hotList');
  if (container) {
    const scrollTop = container.scrollTop;
    const scrollKey = `hotList_${currentTab}_scroll`;
    localStorage.setItem(scrollKey, scrollTop.toString());
    console.log('点击详情前保存滚动位置:', scrollTop, '当前tab:', currentTab, 'key:', scrollKey);
  }

  // 直接跳转到外链
  window.location.href = itemInfo.msgUrl;
};
</script>
<style lang="less" scoped>
.hot {
  display: flex;
  align-items: center;
  min-height: 42px;
  border-bottom: 1px solid #d9d9d9;
  padding: 0px 12px;
  background: #fff;
}
.yuan {
  width: 20px;
  height: 20px;
  margin-right: 6px;
  text-align: center;
  line-height: 20px;
  border-radius: 100%;
  background: linear-gradient(180deg, #808695 0%, #a8a8a8 100%);
  font-family: Arial;
  color: #ffffff;
  font-size: 14px;
}
.van-highlight {
  flex: 1;
}
</style>
