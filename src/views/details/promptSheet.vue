<!-- 提示单详情 -->
<template>
  <div class="promptSheet">
    <!-- <van-overlay :show="show">
      <div class="wrapper" @click.stop>
        <van-loading type="spinner" color="#1989fa" size="50px" />
      </div>
    </van-overlay>
    <van-nav-bar
      title="提示单详情"
      left-text="返回"
      left-arrow
      @click-left="onClickLeft"
    /> -->
    <div class="box">
      <div class="title">舆情提示单</div>
      <div class="id">（编号：{{ info.promptNum }}）</div>
      <div class="table">
        <div class="row">
          <div class="item half">
            <div class="label">发送单位</div>
            <div class="value">{{ info.sendOrgName }}</div>
          </div>
          <div class="item half">
            <div class="label">发送时间</div>
            <div class="value">
              {{ moment(info.sendTime).format("YYYY-MM-DD") }} <br />
              {{ moment(info.sendTime).format("HH:mm:ss") }}
            </div>
          </div>
        </div>
        <div class="row">
          <div class="item half">
            <div class="label">接收人</div>
            <div class="value">{{ info.sendUserName }}</div>
          </div>
          <div class="item half">
            <div class="label">联系电话</div>
            <div class="value">{{ info.sendPhone }}</div>
          </div>
        </div>
        <div class="row">
          <div class="item">
            <div class="label">舆情线索</div>
            <div class="value">
              <InfoContent
                v-if="info.msgTitle"
                :info="{
                  msgTitle: info.msgTitle,
                  msgAbstract: info.msgAbstract,
                  msgContentUrl: info.msgContentUrl,
                  isMsgDel: info.isMsgDel,
                  showDelTip: true,
                }"
              />
            </div>
          </div>
        </div>
        <div class="row">
          <div class="item">
            <div class="label">舆情处 <br />置建议</div>
            <div class="value" style="justify-content: left; text-align: left;">
              {{ info.suggest }}
            </div>
          </div>
        </div>
        <div class="row">
          <div class="item">
            <div class="label">情况反馈</div>
            <div class="value feedback">
              <template v-if="info.promptStatus === 4">
                <div v-if="info.backLogList.length === 1">
                  {{ info.backContent }}
                </div>
                <div class="backContent" v-else>
                  <div class="back-item" v-for="item in info.backLogList">
                    <div class="text">
                      {{ item.logContent }}
                    </div>
                    <div class="time">
                      {{
                        item.createTime
                          ? moment(item.createTime).format(
                              "YYYY-MM-DD HH:mm:ss"
                            )
                          : ""
                      }}
                    </div>
                  </div>
                </div>
                <div
                  class="btn"
                  @click="toFill"
                  v-if="
                    info.isFinish !== 1 &&
                    (organType == '2' || organType == '3')
                  "
                >
                  <van-icon name="records-o" />
                  续报
                </div>
              </template>
              <template v-else>
                <div>
                  {{ feedback }}
                </div>
                <div
                  class="btn"
                  @click="toFill"
                  v-if="
                    info.isFinish !== 1 &&
                    (organType == '2' || organType == '3')
                  "
                >
                  <van-icon name="records-o" />
                  {{
                    feedback || info.promptStatus === 5
                      ? "重新填写反馈内容"
                      : "填写反馈内容"
                  }}
                </div>
              </template>
            </div>
          </div>
        </div>
      </div>
      <div class="remark">
        <div class="item">单位：{{ info.promptOrganName }}</div>
        <div class="item">联系人：{{ info.promptUserName }}</div>
        <div class="item">电话：{{ info.sendUserPhone }}</div>
      </div>
    </div>
  </div>
</template>

<script setup>
//生命周期 - 创建完成（访问当前this实例）
import moment from "moment";
import { stringify, parse } from "flatted";
import InfoContent from "@/components/infoContent/index.vue";
import { onBeforeMount, ref } from "vue";
import { useRoute, useRouter } from "vue-router";
import { findById, getLog, updateMsgStatus } from "@/services/api";
import { jsonToFormData } from "@/utils/formDataUtils";
const info = ref({});
const route = useRoute();
const router = useRouter();
const feedback = ref("");
const show = ref(true);
const organType = ref("1");

const toFill = () => {
  router.push({
    path: "/Details/FillFeedback",
  });
};

const updateMsg = async () => {
  let data = {
    msgNum: info.value.promptNum,
    type: 3,
  };
  const response = await updateMsgStatus(jsonToFormData(data));
};
onBeforeMount(async () => {
  organType.value = localStorage.getItem("organType");
  const response = await findById({
    promptMsgId: route.query.id,
  });
  info.value = response.data;
  updateMsg();
  // getLog('舆情提示','浏览/提示单编号：' + response.data.promptNum)
  getLog("舆情提示", "待办提示单详情页");
  show.value = false;
  let routerFrom = parse(localStorage.getItem("routerFrom"));
  let local = localStorage.getItem("feedback");
  if (routerFrom?.fullPath === "/Details/FillFeedback" && local) {
    //暂存的时候路由跳回来
    feedback.value = local;
    info.value.backLogList.push({
      logContent: feedback.value,
    });
    console.log(info);
  } else {
    feedback.value = localStorage.removeItem("feedback");
  }
  if (response.data.promptStatus === 5 && response.data.backContent) {
    localStorage.setItem("feedback", response.data.backContent);
    feedback.value = response.data.backContent;
  }
  localStorage.setItem("promptSheetDetails", stringify(response.data));
});

const onClickLeft = () => {
  history.back();
};
</script>
<style lang="less" scoped>
/* @import url(); 引入css类 */
.promptSheet {
  .wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
  }
  .box {
    height: calc(~"100vh - 75px");
    overflow-y: auto;
  }
  .title {
    font-weight: 600;
    text-align: center;
    font-size: 18px;
    line-height: 30px;
  }
  .id {
    text-align: center;
  }
  .table {
    background-color: #fff;
    border-top: 1px solid #aaa;
    border-left: 1px solid #aaa;
    margin: 5px;
    font-size: 14px;
    .row {
      display: flex;
      .backContent {
        width: 100%;
        .back-item {
          border-bottom: 1px dotted #aaa;
          &:last-child {
            border: none;
          }
          .text {
            text-align: left;
            padding: 10px 0;
          }
          .time {
            color: #aaa;
            text-align: right;
          }
        }
      }
    }
    .item {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      min-height: 50px;
      & > div {
        border-bottom: 1px solid #aaa;
        border-right: 1px solid #aaa;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-wrap: wrap;
      }
      .label {
        width: 20%;
        text-align: center;
        font-weight: 600;
      }
      .value {
        width: 80%;
        text-align: center;
        word-break: break-all;
        :deep(.infoContent) {
          padding: 10px;
        }
      }
      .feedback {
        padding: 10px;
        width: calc(~"80% - 20px");
        height: calc(~"100% - 20px");
        display: block;
      }
      .Clue {
        height: 400px;
      }
      .content {
        width: 100%;
      }
      .btn {
        display: inline-block;
        margin-top: 10px;
        background-color: #e6f7ff;
        border: 1px solid #228be2;
        border-radius: 5px;
        padding: 0 5px;
        font-size: 14px;
        line-height: 26px;
        .van-icon {
          color: #228be2;
          font-size: 16px;
        }
      }
    }
    .half {
      width: 50%;
      .label {
        width: 40%;
      }
      .value {
        width: 60%;
      }
    }
  }
  .remark {
    padding: 10px;
    font-size: 14px;
  }
}
</style>
