@text-color: #5a5a5a;
@left-menu-background-color: #EFF3F6;
@split-color: #E8E8E8; //分割线颜色
@base-font-size: 14px;

.ellipsis-2 {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    overflow: hidden;
    -webkit-line-clamp: 2;
    /* 控制显示的行数 */
}

.ellipsis-3 {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    overflow: hidden;
    -webkit-line-clamp: 3;
    /* 控制显示的行数 */
}

.ellipsis {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.highlight0 {
    color: red !important;
}

.flex {
    display: flex;
}

.sb {
    justify-content: space-between;
}

.cp {
    cursor: pointer;
}

.dot {
    margin: 0 5px;
    height: 5px;
    width: 5px;
    border-radius: 50%;
}

.no-wrap {
    white-space: nowrap;
}


// .van-loading {
//     text-align: center;
//     padding: 100px;
// }

.van-nav-bar {
    background-color: #63adf8 !important;
    padding: 5px 0;


}

.van-nav-bar__arrow,
.van-nav-bar__title,
.van-nav-bar__text {
    color: #fff !important;
}

.van-icon-searchs {
    font-size: 24px !important;
}