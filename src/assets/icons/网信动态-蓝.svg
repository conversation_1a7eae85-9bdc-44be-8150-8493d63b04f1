<?xml version="1.0" encoding="UTF-8"?>
<svg width="128px" height="128px" viewBox="0 0 128 128" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>网信动态蓝</title>
    <defs>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-1">
            <stop stop-color="#56A3EF" offset="0%"></stop>
            <stop stop-color="#016BDB" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="网信动态蓝">
            <circle id="椭圆形" fill="url(#linearGradient-1)" cx="64" cy="64" r="64"></circle>
            <g id="市场动态备份" transform="translate(26, 26)" fill="#FFFFFF" fill-rule="nonzero">
                <path d="M64.8171946,47.4999891 L40.2257483,47.4999891 L40.2257483,54.6113889 L64.8171946,54.6113889 L64.8171946,47.4999891 Z M64.8171946,33.2228649 L40.2257483,33.2228649 L40.2257483,40.3885894 L64.8171946,40.3885894 L64.8171946,33.2228649 Z M64.8171946,16.6114324 L11.1828925,16.6114324 L11.1828925,23.7228322 L64.8171946,23.7228322 L64.8171946,16.6114324 Z M11.1828925,54.6113889 L31.2686043,54.6113889 L31.2686043,33.2228649 L11.1828925,33.2228649 L11.1828925,54.6113889 Z M66.6628437,76 C65.7400191,73.2314425 63.242821,71.277146 60.3656979,71.277146 C57.4342499,71.277146 54.9913766,73.2314425 54.068552,76 L44.2971459,76 C43.3743213,73.2314425 40.9314479,71.277146 38,71.277146 C35.0685521,71.277146 32.6256787,73.2314425 31.7028541,76 L21.931448,76 C21.0086234,73.2314425 18.5657501,71.277146 15.6343021,71.277146 C12.757179,71.277146 10.2599809,73.2314425 9.33715629,76 L0,76 L0,4.72285397 C0,2.11718343 2.00853633,0 4.45140965,0 L71.5485903,0 C73.9914637,0 76,2.11718343 76,4.72285397 L76,76 L66.6628437,76 L66.6628437,76 Z" id="形状"></path>
            </g>
        </g>
    </g>
</svg>