// src/stores/counter.ts
import { defineStore } from "pinia";
import { getDomainList } from "@/services/api";

export const useDomainListStore = defineStore("domainList", {
  // 状态
  state: () => ({
    list: [],
    organId: localStorage.getItem('organId') || '',
    departmentId: localStorage.getItem('departmentId') || '',    
    userId: localStorage.getItem('userId') || '',   
    userAccount: localStorage.getItem('userAccount') || '',   
    userName: localStorage.getItem('userName') || '',
    iphone: localStorage.getItem('iphone') || '',
    token: localStorage.getItem('token') || ''
  }),
  // 计算属性
  getters: {},
  // 方法
  actions: {
    setDomainList() {
      getDomainList().then((res) => {
        console.log(res);
        this.list = res.data;
      });
    },
    decrement() {
      this.count--;
    },
    setOrganId(id){
      this.organId = id;
    },
    setDepartmentId(id){
      this.departmentId = id;
    },
    setUserId(id){
      this.userId = id;
    },
    setUserAccount(id){
      this.userAccount = id;
    },
    setUserName(id){
      this.userName = id;
    },
    setIphone(id){
      this.iphone = id;
    },
    setToken(id){
      this.token = id;
    }
  },
});
