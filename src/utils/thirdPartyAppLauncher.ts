/**
 * 第三方应用唤起工具类
 * 专门用于小红书链接的应用唤起
 */

interface XiaohongshuConfig {
  appName: string;
  appId: string;         // iOS使用，小红书的scheme
  packageName: string;   // Android使用，小红书的包名
  messageExt: string;    // iOS使用
  param: string;         // Android使用
}

class ThirdPartyAppLauncher {
  // 小红书应用配置
  private static readonly XIAOHONGSHU_CONFIG: XiaohongshuConfig = {
    appName: '小红书',
    appId: 'xhsdiscover://',        // 完整的URL Scheme
    packageName: 'com.xingin.discover',  // 正确的Android包名
    messageExt: 'from=shandong_app',    // 来源标识，可自定义
    param: 'webview'
  };

  // SDK初始化状态
  private static isSDKReady: boolean = false;

  /**
   * 初始化山东通JS SDK
   */
  public static initSDK(): Promise<boolean> {
    return new Promise((resolve) => {
      if (this.isSDKReady) {
        resolve(true);
        return;
      }

      if (!this.isShandongAppEnvironment()) {
        console.log('不在山东通环境中，跳过SDK初始化');
        resolve(false);
        return;
      }

      try {
        // 这里可以添加SDK的配置初始化代码
        // 根据山东通的文档，可能需要调用 wx.config() 等方法
        console.log('山东通JS SDK初始化完成');
        this.isSDKReady = true;
        resolve(true);
      } catch (error) {
        console.error('山东通JS SDK初始化失败:', error);
        resolve(false);
      }
    });
  }

  /**
   * 检测URL是否为小红书链接
   */
  public static isXiaohongshuUrl(url: string): boolean {
    if (!url) return false;
    
    const urlLower = url.toLowerCase();
    return urlLower.includes('xiaohongshu.com') || 
           urlLower.includes('xhslink.com') ||
           urlLower.includes('xhs.link');
  }

  /**
   * 检查是否支持山东通JS SDK
   */
  private static isShandongAppEnvironment(): boolean {
    // 检查是否在山东通app环境中
    const hasWx = typeof window !== 'undefined' &&
                  typeof (window as any).wx !== 'undefined' &&
                  typeof (window as any).wx.invoke === 'function';

    console.log('山东通环境检查:', {
      hasWindow: typeof window !== 'undefined',
      hasWx: typeof (window as any).wx !== 'undefined',
      hasInvoke: typeof (window as any).wx?.invoke === 'function',
      userAgent: navigator.userAgent
    });

    return hasWx;
  }

  /**
   * 唤起小红书应用
   */
  public static async launchXiaohongshu(url: string): Promise<boolean> {

    // 先初始化SDK
    const sdkReady = await this.initSDK();

    return new Promise((resolve) => {
      // 检查是否在山东通环境中 haha
      // if (!sdkReady || !this.isShandongAppEnvironment()) {
      //   console.warn('不在山东通app环境中或SDK未就绪，使用浏览器打开小红书链接');
      //   window.location.href = url;
      //   resolve(false);
      //   return;
      // }

      const config = this.XIAOHONGSHU_CONFIG;

      // 调用山东通JS SDK
      try {
        console.log('尝试唤起小红书应用...', config);

        (window as any).wx.invoke('launch3rdApp', {
          'appName': config.appName,
          'appID': config.appId,
          'messageExt': config.messageExt,
          'packageName': config.packageName,
          'param': url
        }, (res: any) => {
          console.log('小红书应用唤起结果:', res);

          if (res.err_msg && res.err_msg.includes('ok')) {
            console.log('成功唤起小红书应用');
            resolve(true);
          } else {
            console.warn('唤起小红书应用失败，使用浏览器打开', res);
            window.location.href = url;
            resolve(false);
          }
        });
      } catch (error) {
     
        console.error('调用山东通JS SDK失败:', error);
        window.location.href = url;
        resolve(false);
      }
    });
  }

  /**
   * 智能打开链接 - 如果是小红书链接则尝试唤起应用，否则正常打开
   */
  public static async smartOpenUrl(url: string): Promise<boolean> {
    if (this.isXiaohongshuUrl(url)) {
      console.log('检测到小红书链接，尝试唤起小红书应用');
      return await this.launchXiaohongshu(url);
    } else {
      console.log('非小红书链接，使用原有方式打开');
      window.location.href = url;
      return false;
    }
  }
}

export default ThirdPartyAppLauncher;
