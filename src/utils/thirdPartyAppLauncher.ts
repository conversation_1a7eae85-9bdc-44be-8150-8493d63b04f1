/**
 * 第三方应用唤起工具类
 * 专门用于小红书链接的应用唤起
 */

interface XiaohongshuConfig {
  appName: string;
  appId: string;         // iOS使用，小红书的scheme
  packageName: string;   // Android使用，小红书的包名
  messageExt: string;    // iOS使用
  param: string;         // Android使用
}

class ThirdPartyAppLauncher {
  // 小红书应用配置
  private static readonly XIAOHONGSHU_CONFIG: XiaohongshuConfig = {
    appName: '小红书',
    appId: 'xhsdiscover',           // 小红书的URL Scheme
    packageName: 'com.xingin.xhs',  // 小红书的包名
    messageExt: 'from=shandong_app',
    param: 'webview'
  };

  /**
   * 检测URL是否为小红书链接
   */
  public static isXiaohongshuUrl(url: string): boolean {
    if (!url) return false;
    
    const urlLower = url.toLowerCase();
    return urlLower.includes('xiaohongshu.com') || 
           urlLower.includes('xhslink.com') ||
           urlLower.includes('xhs.link');
  }

  /**
   * 检查是否支持山东通JS SDK
   */
  private static isShandongAppEnvironment(): boolean {
    // 检查是否在山东通app环境中
    return typeof window !== 'undefined' && 
           typeof (window as any).wx !== 'undefined' &&
           typeof (window as any).wx.invoke === 'function';
  }

  /**
   * 唤起小红书应用
   */
  public static async launchXiaohongshu(url: string): Promise<boolean> {
    return new Promise((resolve) => {
      // 检查是否在山东通环境中
      if (!this.isShandongAppEnvironment()) {
        console.warn('不在山东通app环境中，使用浏览器打开小红书链接');
        window.location.href = url;
        resolve(false);
        return;
      }

      const config = this.XIAOHONGSHU_CONFIG;

      // 调用山东通JS SDK
      try {
        console.log('尝试唤起小红书应用...');
        
        (window as any).wx.invoke('launch3rdApp', {
          'appName': config.appName,
          'appID': config.appId,
          'messageExt': config.messageExt,
          'packageName': config.packageName,
          'param': config.param
        }, (res: any) => {
          console.log('小红书应用唤起结果:', res);
          
          if (res.err_msg && res.err_msg.includes('ok')) {
            console.log('成功唤起小红书应用');
            resolve(true);
          } else {
            console.warn('唤起小红书应用失败，使用浏览器打开');
            window.location.href = url;
            resolve(false);
          }
        });
      } catch (error) {
        console.error('调用山东通JS SDK失败:', error);
        window.location.href = url;
        resolve(false);
      }
    });
  }

  /**
   * 智能打开链接 - 如果是小红书链接则尝试唤起应用，否则正常打开
   */
  public static async smartOpenUrl(url: string): Promise<boolean> {
    if (this.isXiaohongshuUrl(url)) {
      console.log('检测到小红书链接，尝试唤起小红书应用');
      return await this.launchXiaohongshu(url);
    } else {
      console.log('非小红书链接，使用原有方式打开');
      window.location.href = url;
      return false;
    }
  }
}

export default ThirdPartyAppLauncher;
