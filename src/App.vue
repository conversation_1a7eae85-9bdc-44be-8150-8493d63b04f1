<!--suppress NpmUsedModulesInstalled -->
<script setup>
import { RouterView, useRoute, useRouter } from "vue-router";
import { ref, watch, computed, onMounted } from "vue";
import tips from "./assets/img/tips.png";
import noTips from "./assets/img/notips.png";
import hotList from "./assets/img/hotList.png";
import noHotList from "./assets/img/noHotList.png";
import { useDomainListStore } from "@/stores/domainList";
import { getQxauthCode } from "@/services/api";
import TipPopup from "@/components/TipPopup/index.vue";
const DomainListStore = useDomainListStore();
let active = ref("hotList");
const router = useRouter();
const route = useRoute();
const morePopupVisible = ref(false);
const morePopupPosition = ref({ x: 0, y: 0 });



// 更多操作列表
const moreActions = computed(() => {
  const actions = [];

  // 意见反馈 - 所有用户都可以看到
  actions.push({
    text: '意见反馈',
    value: 'feedback',
    icon: '意见反馈-灰'
  });
  console.log("aaaa",realuserAccount.value);
  // 网安系统 - 特定用户可见
  if (realuserAccount.value == 'zhangrui' || realuserAccount.value == 'limohan'
     || realuserAccount.value == 'zhanglingyu'  || realuserAccount.value == 'hanchuanfeng'
      || realuserAccount.value == 'huangzhenxiang'  || realuserAccount.value == 'yuanxiaoyan'
             || realuserAccount.value == 'hanxia'  || realuserAccount.value == 'chenguohua'
  ) {
    actions.push({
      text: '网安系统',
      value: 'wangan',
      icon: '舆情事件-灰'
    });
  }

  // 历即办 - 特定组织可见
  if (realOrganId.value == 230 && realDepartmentId.value == 230) {
    actions.push({
      text: '历即办',
      value: 'lxqx',
      img: '@/assets/img/lxyq_logo.png'
    });
  }

  // 天晴平台 - 特定组织可见
  if (realOrganId.value == 232 && realDepartmentId.value == 232) {
    actions.push({
      text: '天晴平台',
      value: 'tqqx',
      icon: '我的区县'
    });
  }

  return actions;
});

const realuserId = computed(() => {
  return DomainListStore.userId
})
const realuserAccount = computed(() => {
  return DomainListStore.userAccount
})
const realuserName = computed(() => {
  return DomainListStore.userName
})
const realiphone = computed(() => {
  return DomainListStore.iphone
})
const realtoken = computed(() => {
  return DomainListStore.token
})
const realOrganId = computed(() => {
  return DomainListStore.organId
})
const realDepartmentId = computed(() => {
  return DomainListStore.departmentId
})

// 显示更多弹窗
const showMorePopup = (event) => {
  // 阻止默认的路由跳转行为
  event.preventDefault();
  event.stopPropagation();

  // 计算更多按钮的位置
  const moreButton = event.currentTarget;
  console.log(moreButton)
  if (moreButton) {
    const rect = moreButton.getBoundingClientRect();
    morePopupPosition.value = {
      x: rect.right- 15 , // 右对齐，稍微向左偏移
      y: rect.top + 20    // 在按钮上方显示
    };
  }
  morePopupVisible.value = true;
};



// 处理更多操作选择
const onMoreActionSelect = (action) => {
  morePopupVisible.value = false;

  switch (action.value) {
    case 'feedback':
      goToFeedback();
      break;
    case 'wangan':
      goToWangAn();
      break;
    case 'lxqx':
    case 'tqqx':
      goToMyQx();
      break;
  }
};

// 跳转到意见反馈
const goToFeedback = () => {
  router.push({ path: "/FeedBack" });
};

// 跳转到网安系统
const goToWangAn = () => {
  var url = 'http://*************:20080/ext/api/third/login_app?to=MobileCascadeWorkOrder&userId=' + realuserId.value +
  '&phone=' + realiphone.value + "&token=" + realtoken.value;
  console.log("url:" + url);
  window.open(url, "_blank");
};

// 跳转到历即办/天晴平台
const goToMyQx = () => {
  getQxauthCode().then((res) => {
    if(res.status == 0){
      if(realOrganId.value == 230){
        window.open('/lxYqNginx/?code=' + res.data, "_blank");
      }
      if(realOrganId.value == 232){
        window.open('/tqYqNginx/?code=' + res.data, "_blank");
      }
    }
  });
};

// 清除页面缓存数据的函数
const clearPageCache = (pageName) => {
  console.log('清除页面缓存:', pageName);

  // 根据页面名称清除对应的缓存
  switch (pageName) {
    case 'PublicSentiment':
      // 涉济舆情使用localStorage存储（externalLink模式）
      localStorage.removeItem('publicSentiment_scroll');
      localStorage.removeItem('publicSentiment_data');
      console.log('已清除涉济舆情缓存');
      break;
    case 'hotList':
      // 热搜榜单使用localStorage存储（externalLink模式），需要清除所有tab的数据
      const hotListTabs = ['头条热榜', '头条同城榜', '抖音热榜', '抖音同城榜', '微博热榜', '微博同城榜', '百度热榜', '知乎热榜'];
      hotListTabs.forEach(tab => {
        localStorage.removeItem(`hotList_${tab}_scroll`);
        localStorage.removeItem(`hotList_${tab}_data`);
      });
      // 也清除不带tab的缓存
      localStorage.removeItem('hotList_scroll');
      localStorage.removeItem('hotList_data');
      console.log('已清除热搜榜单缓存');
      break;
    // 其他页面目前没有使用useScrollPosition，不需要清除缓存
    default:
      console.log('页面', pageName, '无需清除缓存');
      break;
  }
};

// 标记是否是手动点击底部tab
let isManualTabClick = false;

// 处理底部tabbar点击事件
const handleTabbarChange = (name) => {
  console.log('手动点击底部tab:', name);
  isManualTabClick = true;
};

watch(active, (newVal, oldVal) => {
  console.log(newVal,oldVal)
  if (
    (window.location.href.indexOf("eventAnalysis/Details") > -1 &&
      active.value == "eventAnalysis") ||
    (window.location.href.indexOf("Details/PromptSheet") > -1 &&
      active.value == "PromptSheet") ||
    (window.location.href.indexOf("NetInfoDynamic/detail") > -1 &&
      active.value == "NetInfoDynamic")
  ) {
    return;
  }

  // 只有手动点击底部tab时才清除缓存
  if (isManualTabClick) {
    clearPageCache(newVal);
    isManualTabClick = false; // 重置标记
  }

  router.push({
    path: "/" + newVal,
  });
});
onMounted(() => {
  let path = window.location.href;
  if (path.indexOf("NetInfoDynamic") > -1) {
    active.value = "NetInfoDynamic";
  } else if (path.indexOf("eventAnalysis") > -1) {
    active.value = "eventAnalysis";
  } else if (path.indexOf("hotList") > -1) {
    active.value = "hotList";
  } else if (path.indexOf("PromptSheet") > -1) {
    active.value = "PromptSheet";
  }else if(path.indexOf("PublicSentiment") > -1){
    active.value = "PublicSentiment";
  }else if(path.indexOf("RefuteRumour") > -1){
    active.value = "RefuteRumour";
  }


  DomainListStore.setDomainList();
});
watch(() => route, (newRoute) => {
  let path = newRoute.path;
  if (path.indexOf("NetInfoDynamic") > -1) {
    active.value = "NetInfoDynamic";
  } else if (path.indexOf("eventAnalysis") > -1) {
    active.value = "eventAnalysis";
  } else if (path.indexOf("hotList") > -1) {
    active.value = "hotList";
  } else if (path.indexOf("PromptSheet") > -1) {
    active.value = "PromptSheet";
  }else if(path.indexOf("PublicSentiment") > -1){
    active.value = "PublicSentiment";
  }else if(path.indexOf("RefuteRumour") > -1){
    active.value = "RefuteRumour";
  }
},{ deep: true})
</script>

<template>
  <!-- <span class="remark" @click="router.push('/FeedBack')">
    <SvgIcon
      width="20"
      height="20"
      :name="'意见反馈'"
    /> 
  </span> -->
  <div style="flex: 1;">
    <RouterView />
  </div>
  
  <van-tabbar v-model="active" v-if="route.path !== '/'" @change="handleTabbarChange">
    <!-- <van-tabbar-item name="NetInfoDynamic" v-if="realuserAccount =='test' || realuserAccount =='chenzhe'"> -->
       <van-tabbar-item name="NetInfoDynamic" >
      <span>网信动态</span>
      <template #icon="props">
        <SvgIcon
          width="20"
          height="20"
          :name="props.active ? '网信动态-蓝' : '网信动态-灰'"
        />
      </template>
    </van-tabbar-item>
    <van-tabbar-item name="hotList">
      <span>热搜榜单</span>
      <template #icon="props">
        <img :src="props.active ? hotList : noHotList" />
      </template>
    </van-tabbar-item>
    <van-tabbar-item name="PublicSentiment">
      <span>涉济舆情</span>
      <template #icon="props">
        <SvgIcon
          width="20"
          height="20"
          :name="props.active ? '涉济舆情-蓝' : '涉济舆情-灰'"
        />
      </template>
    </van-tabbar-item>
    <van-tabbar-item name="PromptSheet">
      <span>提示单</span>
      <template #icon="props">
        <img :src="props.active ? tips : noTips" />
      </template>
    </van-tabbar-item>
    <van-tabbar-item name="eventAnalysis">
      <span>舆情事件</span>
      <template #icon="props">
        <SvgIcon
          width="20"
          height="20"
          :name="props.active ? '舆情事件-蓝' : '舆情事件-灰'"
        /> </template
    ></van-tabbar-item>
    <van-tabbar-item name="RefuteRumour">
      <span>辟谣信息</span>
      <template #icon="props">
        <SvgIcon
          width="20"
          height="20"
          :name="props.active ? '辟谣信息-蓝' : '辟谣信息-灰'"
        /> </template
    ></van-tabbar-item>
    <!-- 自定义更多按钮，不使用van-tabbar-item -->
    <div class="custom-tabbar-item" @click="showMorePopup">
      <div class="custom-tabbar-icon">
        <SvgIcon
          width="20"
          height="20"
          name="更多-灰"
        />
      </div>
      <span class="custom-tabbar-text">更多</span>
    </div>
  </van-tabbar>

  <!-- 更多功能弹窗 -->
  <TipPopup
    v-model:visible="morePopupVisible"
    :actions="moreActions"
    :position="morePopupPosition"
    placement="top-right"
    @select="onMoreActionSelect"
  />

  <!-- 备用弹窗样式（如果需要自定义样式） -->
  <!-- <van-popup v-model:show="morePopupVisible" position="bottom" round :style="{ padding: '20px 0' }">
    <div class="more-popup">
      <div class="more-content">
        <div class="more-item" @click="goToFeedback" v-if="true">
          <van-icon name="chat-o" />
          <span>意见反馈</span>
        </div>
        <div class="more-item" @click="goToWangAn" v-if="realuserAccount == 'zhangrui' || realuserAccount == 'limohan'">
          <van-icon name="shield-o" />
          <span>网安系统</span>
        </div>
        <div class="more-item" @click="goToMyQx" v-if="(realOrganId == 230 && realDepartmentId == 230)">
          <van-icon name="manager-o" />
          <span>历即办</span>
        </div>
        <div class="more-item" @click="goToMyQx" v-if="(realOrganId == 232 && realDepartmentId == 232)">
          <van-icon name="setting-o" />
          <span>天晴平台</span>
        </div>
      </div>
    </div>
  </van-popup> -->
</template>

<style lang="less" scoped>
.remark {
  left: 85%;
  top: 3px;
  position: absolute;
  display: flex;
  z-index: 2005;
}

.custom-tabbar-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  flex: 1;
  padding: 5px 0;
  cursor: pointer;
  transition: all 0.2s;
}

.custom-tabbar-item:active {
  opacity: 0.7;
}

.custom-tabbar-icon {
  margin-bottom: 2px;
  font-size:0
}

.custom-tabbar-text {
  font-size: 12px;
  color: #323233;
  line-height: 1;
}

/deep/.van-tabbar-item__icon{
  font-size:0
}

</style>
