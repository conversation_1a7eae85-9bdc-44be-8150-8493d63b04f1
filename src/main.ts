import './assets/main.css'
import './assets/common.less'
import { createApp } from 'vue'
import { createPinia } from 'pinia'
import 'virtual:svg-icons-register'
import App from './App.vue'
import router from './router'
import http from './services/http';
import gloablComponent from './components/index';
import ThirdPartyAppLauncher from './utils/thirdPartyAppLauncher';


const app = createApp(App)
app.config.globalProperties.$http = http;
app.use(gloablComponent);
// 全局注册 SvgIcon 组件
app.use(createPinia())
app.use(router)

// 初始化山东通JS SDK
ThirdPartyAppLauncher.initSDK().then((success) => {
  if (success) {
    console.log('山东通JS SDK初始化成功');
  } else {
    console.log('山东通JS SDK初始化失败或不在山东通环境中');
  }
});

app.mount('#app')
