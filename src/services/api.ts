// src/services/api.ts
import http from './http';

export const getDomainList = () => {
  return http.get('/classifyConfig/classifyList?type=1');
}

// 查询用户信息
export const getUserInfo = (params: {
  code: any
}) => {
  return http.get('login/user', { params })
}

// 查询提示单列表
export const findAllAPI = (params: any) => {
  return http.get('/api/prompt/findAllAPI', { params });
};

// 查询提示单列表（市办单位)
export const findAll = (params: any) => {
  return http.get('/prompt/findAll', { params });
};

// 根据提示单id查询信息
export const findById = (params: any) => {
  return http.get('/api/prompt/findById', { params });
};

// 提交反馈
export const updateMsgStatus = (data: any) => {
  return http.post(`/api/prompt/updateMsgStatus`, data);
};

// 查询热榜列表
export const hotListfindList = (params: any) => {
  return http.get('/hotrank/list/1', { params });
};

// 查询事件总数
export const eventListfindAll = (params: any) => {
  return http.get('/monitor/event/count', { params });
};
// 查询事件列表
export const eventListfindList = (params: any) => {
  return http.get('/monitor/event/list', { params });
};

// 查询事件详情
export const getEventInfo = (params: any) => {
  return http.get('/monitor/event/detail', { params });
};

// 查询事件详情概览
export const getEventDataDesc = (params: any) => {
  return http.get('/monitor/event/msgNumsDesc', { params });
};

// 查询事件详情概览舆论场数据
export const getEventDataSituation = (params: any) => {
  return http.get('/monitor/event/msgNums', { params });
};


// 查询事件详情传播趋势数据
export const getEventSpreadTends = (params: any) => {
  return http.get('/monitor/event/spreadTends', { params });
};

// 添加日志
export const getLog = (module: any, content: any) => {
  let url = "/login/addLog";
  let params = {
    module: module,
    content: content ? content : "浏览",

  };
  return http.get(url, { params });
};

export const getLogDetial = (module: any, content: any,title:any,spark:any) => {
  let url = "/login/addLog";
  let params = {
    module: module,
    content: content ? content : "浏览",
    title: title ? title : "",
    spark:spark?spark:""
  };
  return http.get(url, { params });
};

// 查询涉济舆情总数
export const getJNPublicSentimentCount = (params: any) => {
  return http.get('/recommend/msgCount/1', { params });
};

// 查询涉济舆情列表
export const getJNPublicSentimentList = (params: any) => {
  return http.get('/recommend/msgList/1', { params });
};

// 查询辟谣信息列表
export const getRefuteRumourList = (params: any) => {
  return http.get('/piyao/list', { params });
};



// 查询区县授权码
export const getQxauthCode = () => {
  return http.get('/open/authCode');
};

// 新增意见建议提交
export const postFeedback = (data: any) => {
  return http.post(`/recommend/feedback`, data);
};

// 获取网信动态列表
export const getWorkDynamic = (params: any) => {
  return http.get('/website/workDynamic', { params });
};

// 获取网信动态详情
export const getWorkDynamicDetail = (params: any) => {
  return http.get('/website/workDynamic/detail', { params });
};

// 添加阅读数统计接口
export const addReadCount = (docid: string | number, docchannel: number, siteid: number = 3) => {
  let url = "/website/readCount/add";

  // 使用 FormData 发送 multipart/form-data 请求
  const formData = new FormData();
  formData.append('docid', docid.toString());
  formData.append('docchannel', docchannel.toString());
  formData.append('siteid', siteid.toString());

  return http.post(url, formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  });
};